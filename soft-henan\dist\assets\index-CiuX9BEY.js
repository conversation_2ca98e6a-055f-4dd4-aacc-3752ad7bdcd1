import{_ as M,r as n,o as G,y as H,e as C,z as J,A as Q,c as o,a as t,b as p,d as y,f as k,h as l,i,F as L,j as q,t as c}from"./index-DIypJWEE.js";const W={class:"policy-container"},X={class:"container"},Y={class:"policy-content"},Z={class:"min-height-container"},ee={class:"policy-list"},ae={key:0,class:"empty-data"},te={class:"policy-image"},se=["src","alt"],oe={class:"policy-info"},le={class:"policy-date"},ne={class:"policy-title"},ie={class:"policy-desc"},ce=["onClick"],ue={key:1,class:"pagination-container"},re={class:"policy-list"},de={key:0,class:"empty-data"},pe={class:"policy-image"},ge=["src","alt"],ve={class:"policy-info"},_e={class:"policy-date"},he={class:"policy-title"},ye={class:"policy-desc"},me=["onClick"],Se={key:1,class:"pagination-container"},ze={class:"policy-list"},Ce={key:0,class:"empty-data"},ke={class:"policy-image"},fe=["src","alt"],we={class:"policy-info"},Ne={class:"policy-date"},be={class:"policy-title"},Pe={class:"policy-desc"},De=["onClick"],Te={key:1,class:"pagination-container"},xe={__name:"index",setup(Le){const R=n(!1),f=n("1"),g=n([]),v=n([]),_=n([]),u=n({pageNum:1,pageSize:10}),r=n({pageNum:1,pageSize:10}),d=n({pageNum:1,pageSize:10}),m=n(0),S=n(0),z=n(0);G(()=>{console.log("政策法规页面已加载"),U()});const U=async()=>{await Promise.all([w(),N(),b()])},w=async()=>{try{const e=await H(u.value);e&&e.data?(g.value=e.data.list||[],m.value=e.data.total||0):(g.value=[],m.value=0)}catch(e){console.error("获取政策文件数据失败:",e),C.error("获取政策文件数据失败"),g.value=[],m.value=0}},N=async()=>{try{const e=await J(r.value);e&&e.data?(v.value=e.data.list||[],S.value=e.data.total||0):(v.value=[],S.value=0)}catch(e){console.error("获取法律法规数据失败:",e),C.error("获取法律法规数据失败"),v.value=[],S.value=0}},b=async()=>{try{const e=await Q(d.value);e&&e.data?(_.value=e.data.list||[],z.value=e.data.total||0):(_.value=[],z.value=0)}catch(e){console.error("获取规章制度数据失败:",e),C.error("获取规章制度数据失败"),_.value=[],z.value=0}},P=e=>e?new Date(e).toLocaleDateString("zh-CN"):"",$=e=>{f.value=e,console.log("切换到标签页:",e)},B=(e,s)=>{u.value.pageNum=e,u.value.pageSize=s,w()},I=(e,s)=>{u.value.pageNum=1,u.value.pageSize=s,w()},O=(e,s)=>{r.value.pageNum=e,r.value.pageSize=s,N()},V=(e,s)=>{r.value.pageNum=1,r.value.pageSize=s,N()},A=(e,s)=>{d.value.pageNum=e,d.value.pageSize=s,b()},F=(e,s)=>{d.value.pageNum=1,d.value.pageSize=s,b()},D=e=>{console.log("查看政策详情:",e.title),e.contentId?window.open(`/policy/detail?id=${e.contentId}`,"_blank"):C.info("政策详情功能开发中...")};return(e,s)=>{const T=k("a-pagination"),x=k("a-tab-pane"),j=k("a-tabs"),E=k("a-spin");return l(),o("div",W,[t("div",X,[s[4]||(s[4]=t("h1",{class:"page-title"},"政策法规",-1)),t("div",Y,[p(E,{spinning:R.value,tip:"加载中..."},{default:y(()=>[t("div",Z,[p(j,{activeKey:f.value,"onUpdate:activeKey":s[3]||(s[3]=a=>f.value=a),onChange:$},{default:y(()=>[p(x,{key:"1",tab:"政策文件"},{default:y(()=>[t("div",ee,[g.value.length===0?(l(),o("div",ae," 暂无政策文件数据 ")):i("",!0),(l(!0),o(L,null,q(g.value,(a,h)=>(l(),o("div",{class:"policy-item",key:h},[t("div",te,[a.image?(l(),o("img",{key:0,src:a.image,alt:a.title},null,8,se)):i("",!0)]),t("div",oe,[t("p",le,c(P(a.publishTime)),1),t("h3",ne,c(a.title),1),t("p",ie,c(a.summary),1),t("a",{onClick:K=>D(a),class:"read-more"},"阅读更多",8,ce)])]))),128)),g.value.length>0?(l(),o("div",ue,[p(T,{current:u.value.pageNum,"onUpdate:current":s[0]||(s[0]=a=>u.value.pageNum=a),total:m.value,pageSize:u.value.pageSize,onChange:B,showSizeChanger:"",pageSizeOptions:["5","10","20"],onShowSizeChange:I},null,8,["current","total","pageSize"])])):i("",!0)])]),_:1}),p(x,{key:"2",tab:"法律法规"},{default:y(()=>[t("div",re,[v.value.length===0?(l(),o("div",de," 暂无法律法规数据 ")):i("",!0),(l(!0),o(L,null,q(v.value,(a,h)=>(l(),o("div",{class:"policy-item",key:h},[t("div",pe,[a.image?(l(),o("img",{key:0,src:a.image,alt:a.title},null,8,ge)):i("",!0)]),t("div",ve,[t("p",_e,c(P(a.publishTime)),1),t("h3",he,c(a.title),1),t("p",ye,c(a.summary),1),t("a",{onClick:K=>D(a),class:"read-more"},"阅读更多",8,me)])]))),128)),v.value.length>0?(l(),o("div",Se,[p(T,{current:r.value.pageNum,"onUpdate:current":s[1]||(s[1]=a=>r.value.pageNum=a),total:S.value,pageSize:r.value.pageSize,onChange:O,showSizeChanger:"",pageSizeOptions:["5","10","20"],onShowSizeChange:V},null,8,["current","total","pageSize"])])):i("",!0)])]),_:1}),p(x,{key:"3",tab:"规章制度"},{default:y(()=>[t("div",ze,[_.value.length===0?(l(),o("div",Ce," 暂无规章制度数据 ")):i("",!0),(l(!0),o(L,null,q(_.value,(a,h)=>(l(),o("div",{class:"policy-item",key:h},[t("div",ke,[a.image?(l(),o("img",{key:0,src:a.image,alt:a.title},null,8,fe)):i("",!0)]),t("div",we,[t("p",Ne,c(P(a.publishTime)),1),t("h3",be,c(a.title),1),t("p",Pe,c(a.summary),1),t("a",{onClick:K=>D(a),class:"read-more"},"阅读更多",8,De)])]))),128)),_.value.length>0?(l(),o("div",Te,[p(T,{current:d.value.pageNum,"onUpdate:current":s[2]||(s[2]=a=>d.value.pageNum=a),total:z.value,pageSize:d.value.pageSize,onChange:A,showSizeChanger:"",pageSizeOptions:["5","10","20"],onShowSizeChange:F},null,8,["current","total","pageSize"])])):i("",!0)])]),_:1})]),_:1},8,["activeKey"])])]),_:1},8,["spinning"])])])])}}},Ke=M(xe,[["__scopeId","data-v-a28dd725"]]);export{Ke as default};
