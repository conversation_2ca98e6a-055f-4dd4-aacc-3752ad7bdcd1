import{_ as E,r as c,K as J,o as K,c as U,P as S,a as t,b as n,d as i,f as d,s as Q,h as u,Q as T,R as C,n as f,i as m,t as p,D as M,l as N,S as G,e as v}from"./index-DIypJWEE.js";import{g as H,s as W}from"./registration-Dy1-3mUd.js";const Z={class:"registration-container"},ee={class:"main-content"},te={class:"form-container"},ae={class:"form-steps"},ne={class:"form-section"},se={class:"form-section"},le={class:"attendee-selector"},ie={class:"attendee-buttons"},oe={class:"form-section"},de={class:"fee-card"},re={class:"fee-item"},ue={class:"fee-info"},ve={class:"fee-detail"},me={class:"fee-value"},pe={key:0,class:"fee-item"},ce={class:"fee-info"},fe={class:"fee-detail"},be={class:"fee-value"},ge={class:"fee-total"},_e={class:"fee-amount"},xe={class:"invoice-selection"},ye={class:"form-agreement"},ke={class:"form-navigation"},Ne={__name:"index",setup(Ie){const D=Q(),I=c(!1),_=c(300),x=c(200),X=c(300),y=c(!1),o=c(0),a=J({name:"",phone:"",hospital:"",attendees:1,teamMembers:"",time:null,dinner:!1,specialNeeds:"",needInvoice:!1,invoiceTitle:"",taxNumber:"",agreeTerms:!1});K(()=>{q()});const q=async()=>{try{const l=await H();l&&l.data&&(_.value=l.data.feePerPerson||300,l.data.dinnerFee&&(x.value=l.data.dinnerFee),k())}catch(l){console.error("获取费用信息失败",l)}},k=()=>{let l=a.attendees*_.value;a.dinner&&(l+=a.attendees*x.value),X.value=l},F=()=>{a.needInvoice||(a.invoiceTitle="",a.taxNumber="")},V=l=>l&&l<G().startOf("day"),P=()=>{y.value=!0},B=async()=>{if(o.value===0){if(!a.name){v.warning("请输入姓名");return}if(!a.phone){v.warning("请输入手机号码");return}if(!/^1[3-9]\d{9}$/.test(a.phone)){v.warning("请输入正确的手机号码");return}if(!a.hospital){v.warning("请输入医院或单位名称");return}}if(o.value===1&&!a.time){v.warning("请选择参会时间");return}o.value++},Y=()=>{o.value--},L=async()=>{if(!a.agreeTerms){v.warning("请阅读并同意参会须知与条款");return}I.value=!0;try{const l={name:a.name,phone:a.phone,hospital:a.hospital,attendees:a.attendees,teamMembers:a.teamMembers||"",time:a.time.format("YYYY-MM-DD"),dinner:a.dinner,specialNeeds:a.specialNeeds||"",needInvoice:a.needInvoice,invoiceTitle:a.invoiceTitle||"",taxNumber:a.taxNumber||"",totalFee:X.value},e=await W(l);e&&e.code===1?(localStorage.setItem("registrationData",JSON.stringify({...l,orderId:e.data.orderId})),D.push("/registration/payment")):v.error(e.msg||"提交失败，请稍后重试")}catch(l){console.error("提交报名信息失败",l),v.error("提交失败，请稍后重试")}finally{I.value=!1}};return(l,e)=>{const w=d("a-step"),O=d("a-steps"),b=d("a-input"),r=d("a-form-item"),R=d("a-input-number"),g=d("a-button"),h=d("a-textarea"),$=d("a-date-picker"),j=d("a-checkbox"),z=d("a-form"),A=d("a-modal");return u(),U("div",Z,[e[33]||(e[33]=S('<div class="registration-banner" data-v-146a9ebe><div class="banner-content" data-v-146a9ebe><div class="banner-title-area" data-v-146a9ebe><h1 data-v-146a9ebe>河南省软组织病研究会</h1><h2 class="meeting-title" data-v-146a9ebe>学术交流会议报名</h2></div><div class="meeting-notice" data-v-146a9ebe><span class="notice-icon" data-v-146a9ebe>i</span><span data-v-146a9ebe>为确保会议顺利进行，请务必填写真实信息</span></div></div></div>',1)),t("div",ee,[t("div",te,[e[31]||(e[31]=t("div",{class:"form-header"},[t("i",{class:"form-icon"}),t("span",null,"填写报名信息")],-1)),t("div",ae,[n(O,{current:o.value,size:"small",responsive:!1},{default:i(()=>[n(w,{title:"基本信息"}),n(w,{title:"参会信息"}),n(w,{title:"费用信息"})]),_:1},8,["current"])]),n(z,{model:a,name:"registration",layout:"vertical",autocomplete:"off",onFinish:L,class:"custom-form"},{default:i(()=>[T(t("div",null,[t("div",ne,[e[16]||(e[16]=t("div",{class:"section-title"},[t("span",{class:"section-icon"},"👤"),t("span",null,"基本信息")],-1)),n(r,{label:"姓名",name:"name",rules:[{required:!0,message:"请输入姓名"}]},{default:i(()=>[n(b,{value:a.name,"onUpdate:value":e[0]||(e[0]=s=>a.name=s),placeholder:"请输入您的真实姓名",maxLength:20},null,8,["value"])]),_:1}),n(r,{label:"手机号码",name:"phone",rules:[{required:!0,message:"请输入手机号码"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码"}]},{default:i(()=>[n(b,{value:a.phone,"onUpdate:value":e[1]||(e[1]=s=>a.phone=s),placeholder:"用于接收会议通知",maxLength:11},null,8,["value"])]),_:1}),n(r,{label:"医院/单位",name:"hospital",rules:[{required:!0,message:"请输入医院或单位名称"}]},{default:i(()=>[n(b,{value:a.hospital,"onUpdate:value":e[2]||(e[2]=s=>a.hospital=s),placeholder:"请输入您所在的医院或单位",maxLength:50},null,8,["value"])]),_:1})])],512),[[C,o.value===0]]),T(t("div",null,[t("div",se,[e[19]||(e[19]=t("div",{class:"section-title"},[t("span",{class:"section-icon"},"📅"),t("span",null,"参会信息")],-1)),n(r,{label:"参会人数",name:"attendees",rules:[{required:!0,message:"请选择参会人数"}]},{default:i(()=>[t("div",le,[n(R,{value:a.attendees,"onUpdate:value":e[3]||(e[3]=s=>a.attendees=s),min:1,max:20,onChange:k,class:"attendee-input"},null,8,["value"]),t("div",ie,[n(g,{class:"attendee-btn",onClick:e[4]||(e[4]=()=>{a.attendees>1&&(a.attendees--,k())}),disabled:a.attendees<=1},{default:i(()=>e[17]||(e[17]=[t("span",null,"-",-1)])),_:1},8,["disabled"]),n(g,{class:"attendee-btn",onClick:e[5]||(e[5]=()=>{a.attendees++,k()})},{default:i(()=>e[18]||(e[18]=[t("span",null,"+",-1)])),_:1})])])]),_:1}),a.attendees>1?(u(),f(r,{key:0,label:"团队成员",name:"teamMembers"},{default:i(()=>[n(h,{value:a.teamMembers,"onUpdate:value":e[6]||(e[6]=s=>a.teamMembers=s),rows:3,placeholder:"请输入其他参会人员的姓名、电话，每人一行"},null,8,["value"])]),_:1})):m("",!0),n(r,{label:"参会时间",name:"time",rules:[{required:!0,message:"请选择参会时间"}]},{default:i(()=>[n($,{value:a.time,"onUpdate:value":e[7]||(e[7]=s=>a.time=s),style:{width:"100%"},"disabled-date":V,placeholder:"请选择参加会议的日期"},null,8,["value"])]),_:1}),n(r,{label:"特殊需求",name:"specialNeeds"},{default:i(()=>[n(h,{value:a.specialNeeds,"onUpdate:value":e[8]||(e[8]=s=>a.specialNeeds=s),rows:2,placeholder:"可选，如有饮食禁忌或其他需求请在此说明"},null,8,["value"])]),_:1})])],512),[[C,o.value===1]]),T(t("div",null,[t("div",oe,[e[27]||(e[27]=t("div",{class:"section-title"},[t("span",{class:"section-icon"},"💰"),t("span",null,"费用信息")],-1)),t("div",de,[t("div",re,[t("div",ue,[e[20]||(e[20]=t("span",{class:"fee-label"},"参会基本费用：",-1)),t("div",ve,"每人 ¥"+p(_.value)+"元 × "+p(a.attendees)+"人",1)]),t("div",me,"¥"+p((_.value*a.attendees).toFixed(2)),1)]),a.dinner?(u(),U("div",pe,[t("div",ce,[e[21]||(e[21]=t("span",{class:"fee-label"},"晚宴费用：",-1)),t("div",fe,"每人 ¥"+p(x.value)+"元 × "+p(a.attendees)+"人",1)]),t("div",be,"¥"+p((x.value*a.attendees).toFixed(2)),1)])):m("",!0),e[23]||(e[23]=t("div",{class:"fee-divider"},null,-1)),t("div",ge,[e[22]||(e[22]=t("span",null,"应付总额：",-1)),t("span",_e,"¥"+p(X.value.toFixed(2)),1)])]),n(r,{label:"发票信息",name:"invoice"},{default:i(()=>[t("div",xe,[t("div",{class:M(["invoice-option",{active:!a.needInvoice}]),onClick:e[9]||(e[9]=()=>{a.needInvoice=!1,F()})},e[24]||(e[24]=[t("div",{class:"invoice-icon"},"🚫",-1),t("div",null,"不需要发票",-1)]),2),t("div",{class:M(["invoice-option",{active:a.needInvoice}]),onClick:e[10]||(e[10]=()=>{a.needInvoice=!0,F()})},e[25]||(e[25]=[t("div",{class:"invoice-icon"},"🧾",-1),t("div",null,"需要发票",-1)]),2)])]),_:1}),a.needInvoice?(u(),f(r,{key:0,label:"发票抬头",name:"invoiceTitle",rules:[{required:a.needInvoice,message:"请输入发票抬头"}]},{default:i(()=>[n(b,{value:a.invoiceTitle,"onUpdate:value":e[11]||(e[11]=s=>a.invoiceTitle=s),placeholder:"请输入发票抬头"},null,8,["value"])]),_:1},8,["rules"])):m("",!0),a.needInvoice?(u(),f(r,{key:1,label:"纳税人识别号",name:"taxNumber"},{default:i(()=>[n(b,{value:a.taxNumber,"onUpdate:value":e[12]||(e[12]=s=>a.taxNumber=s),placeholder:"请输入纳税人识别号"},null,8,["value"])]),_:1})):m("",!0),t("div",ye,[n(j,{checked:a.agreeTerms,"onUpdate:checked":e[13]||(e[13]=s=>a.agreeTerms=s)},{default:i(()=>[e[26]||(e[26]=N(" 我已阅读并同意")),t("a",{href:"javascript:void(0)",onClick:P},"《参会须知与条款》")]),_:1},8,["checked"])])])],512),[[C,o.value===2]]),t("div",ke,[o.value>0?(u(),f(g,{key:0,onClick:Y,class:"prev-btn"},{default:i(()=>e[28]||(e[28]=[t("span",{class:"btn-icon"},"←",-1),N(" 上一步 ")])),_:1})):m("",!0),o.value<2?(u(),f(g,{key:1,type:"primary",onClick:B,class:"next-btn"},{default:i(()=>e[29]||(e[29]=[N(" 下一步 "),t("span",{class:"btn-icon"},"→",-1)])),_:1})):m("",!0),o.value===2?(u(),f(g,{key:2,type:"primary","html-type":"submit",loading:I.value,disabled:!a.agreeTerms,class:"submit-btn"},{default:i(()=>e[30]||(e[30]=[N(" 提交并支付 ")])),_:1},8,["loading","disabled"])):m("",!0)])]),_:1},8,["model"])])]),e[34]||(e[34]=S('<div class="footer-info" data-v-146a9ebe><div class="info-item" data-v-146a9ebe><i class="info-icon location-icon" data-v-146a9ebe></i><span data-v-146a9ebe><strong data-v-146a9ebe>会议地点：</strong>郑州市XXXX酒店（XXXX路XX号）</span></div><div class="info-item" data-v-146a9ebe><i class="info-icon phone-icon" data-v-146a9ebe></i><span data-v-146a9ebe><strong data-v-146a9ebe>联系方式：</strong>0371-XXXXXXXX（赵老师）</span></div><div class="info-item" data-v-146a9ebe><i class="info-icon org-icon" data-v-146a9ebe></i><span data-v-146a9ebe><strong data-v-146a9ebe>主办单位：</strong>河南省软组织病研究会</span></div></div>',1)),n(A,{visible:y.value,"onUpdate:visible":e[14]||(e[14]=s=>y.value=s),title:"参会须知与条款",onOk:e[15]||(e[15]=s=>y.value=!1)},{default:i(()=>e[32]||(e[32]=[t("div",{class:"terms-content"},[t("h3",null,"一、报名须知"),t("p",null,"1. 请确保提供的个人信息真实有效，以便会务组准确安排会议相关事宜。"),t("p",null,"2. 报名成功后，会务组将通过短信向您发送确认信息。"),t("p",null,"3. 会议资料将于会议当天发放，请凭报名确认短信签到领取。"),t("h3",null,"二、付款与发票"),t("p",null,"1. 报名费用包含会议资料、茶歇等，住宿费用自理。"),t("p",null,"2. 如需发票，请在报名时提供准确的开票信息。"),t("p",null,"3. 发票将在会议结束后一个月内寄出或发送电子版。"),t("h3",null,"三、退款政策"),t("p",null,"1. 会议开始前7天以上取消报名，全额退款。"),t("p",null,"2. 会议开始前3-7天取消报名，退款50%。"),t("p",null,"3. 会议开始前3天内取消报名，不予退款。"),t("h3",null,"四、其他规定"),t("p",null,"1. 会议期间，请遵守会场秩序，保持安静。"),t("p",null,"2. 如有特殊情况需要离场，请提前告知会务人员。"),t("p",null,"3. 主办方保留对会议议程调整的权利。")],-1)])),_:1},8,["visible"])])}}},Te=E(Ne,[["__scopeId","data-v-146a9ebe"]]);export{Te as default};
