<template>
  <div class="home">
    <div class="container">
      <!-- 轮播图 -->
      <carousel />

      <div class="main-content">
        <div class="left-column">
          <!-- 新闻资讯 -->
          <news-list
            title="新闻资讯"
            moreLink="/news"
            :newsList="newsData"
          />

          <!-- 学会通知 -->
          <news-list
            title="学会通知"
            moreLink="/about"
            :newsList="noticeData"
          />
        </div>

        <div class="right-column">
          <!-- 政策发布 -->
          <news-list
            title="政策发布"
            moreLink="/policy"
            :newsList="policyData"
          />

          <!-- 法律法规 -->
          <news-list
            title="法律法规"
            moreLink="/policy"
            :newsList="lawData"
          />
        </div>
      </div>

      <!-- 服务中心 -->
      <div class="service-section">
        <h2 class="section-title">服务中心</h2>
        <div class="divider"></div>
        <p class="section-description">我会致力于为会员提供专业的服务与支持，促进软组织病研究与临床应用的发展。</p>
        
        <div class="service-grid">
          <div class="service-card" v-for="(item, index) in serviceData" :key="index">
            <div class="card-image">
              <img v-if="item.coverImage" :src="item.coverImage" :alt="item.title" />
              <div v-else class="default-icon">🛠️</div>
            </div>
            <div class="card-content">
              <h3 class="card-title">{{ item.title }}</h3>
              <p class="card-desc">{{ item.summary || item.description || '暂无描述' }}</p>
              <router-link :to="`/service/detail/${item.contentId}`" class="more-link">了解更多</router-link>
            </div>
          </div>
        </div>
        
        <div class="service-more">
          <router-link to="/service" class="more-btn">查看更多服务</router-link>
        </div>
      </div>

      <!-- 公益活动 -->
      <div class="charity-section">
        <h2 class="section-title">公益活动</h2>
        <div class="divider"></div>
        <activity-highlight
          title="公益活动"
          moreLink="/activity"
          :showPagination="true"
        />
      </div>

      <!-- 单位会员之窗 -->
      <member-units
        title="单位会员之窗"
        moreLink="/join"
        :members="memberData"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { getContentByType, getRecommendContent, getTopContent } from '../api/content'

import Carousel from '../components/home/<USER>'
import NewsList from '../components/home/<USER>'
import ActivityHighlight from '../components/home/<USER>'
import MemberUnits from '../components/home/<USER>'
import memberLogo from '../assets/images/member.svg'

// 响应式数据
const newsData = ref([])
const noticeData = ref([])
const policyData = ref([])
const lawData = ref([])
const memberData = ref([])
const serviceData = ref([])

// 页面加载
onMounted(() => {
  queryHomeData()
})

// 查询首页数据
const queryHomeData = async () => {
  try {
    await Promise.all([
      queryNewsData(),
      queryNoticeData(),
      queryPolicyData(),
      queryLawData(),
      queryMemberData(),
      queryServiceData()
    ])
  } catch (error) {
    console.error('获取首页数据失败:', error)
    message.error('获取首页数据失败')
  }
}

// 查询新闻数据
const queryNewsData = async () => {
  try {
    // 使用getAllContentTypes和queryContent来获取新闻内容
    const typesRes = await getAllContentTypes()
    if (typesRes && (typesRes.code === 0 || typesRes.code === 1 || typesRes.code === 200) && typesRes.data) {
      const newsType = typesRes.data.find(type => type.contentTypeCode === 'news')
      if (newsType) {
        const params = {
          contentTypeId: newsType.contentTypeId,
          pageNum: 1,
          pageSize: 8,
          status: 1,
          deletedFlag: false
        }
        const result = await queryContent(params)
        if (result && result.data && result.data.list) {
          newsData.value = result.data.list.map(item => ({
            title: item.title,
            link: `/news/detail/${item.contentId}`,
            date: formatDate(item.publishTime)
          }))
        } else {
          newsData.value = []
        }
      } else {
        newsData.value = []
      }
    } else {
      newsData.value = []
    }
  } catch (error) {
    console.error('获取新闻数据失败:', error)
    newsData.value = []
  }
}

// 查询通知数据
const queryNoticeData = async () => {
  try {
    // 使用getAllContentTypes和queryContent来获取通知内容
    const typesRes = await getAllContentTypes()
    if (typesRes && (typesRes.code === 0 || typesRes.code === 1 || typesRes.code === 200) && typesRes.data) {
      const noticeType = typesRes.data.find(type => type.contentTypeCode === 'notice')
      if (noticeType) {
        const params = {
          contentTypeId: noticeType.contentTypeId,
          pageNum: 1,
          pageSize: 8,
          status: 1,
          deletedFlag: false
        }
        const result = await queryContent(params)
        if (result && result.data && result.data.list) {
          noticeData.value = result.data.list.map(item => ({
            title: item.title,
            link: `/news/detail/${item.contentId}`,
            date: formatDate(item.publishTime)
          }))
        } else {
          noticeData.value = []
        }
      } else {
        noticeData.value = []
      }
    } else {
      noticeData.value = []
    }
  } catch (error) {
    console.error('获取通知数据失败:', error)
    noticeData.value = []
  }
}

// 查询政策数据
const queryPolicyData = async () => {
  try {
    // 使用getAllContentTypes和queryContent来获取政策内容
    const typesRes = await getAllContentTypes()
    if (typesRes && (typesRes.code === 0 || typesRes.code === 1 || typesRes.code === 200) && typesRes.data) {
      const policyType = typesRes.data.find(type => type.contentTypeCode === 'policy')
      if (policyType) {
        const params = {
          contentTypeId: policyType.contentTypeId,
          pageNum: 1,
          pageSize: 4,
          status: 1,
          deletedFlag: false
        }
        const result = await queryContent(params)
        if (result && result.data && result.data.list) {
          policyData.value = result.data.list.map(item => ({
            title: item.title,
            link: `/policy/detail/${item.contentId}`,
            date: formatDate(item.publishTime)
          }))
        } else {
          policyData.value = []
        }
      } else {
        policyData.value = []
      }
    } else {
      policyData.value = []
    }
  } catch (error) {
    console.error('获取政策数据失败:', error)
    policyData.value = []
  }
}

// 查询法律法规数据
const queryLawData = async () => {
  try {
    // 使用getAllContentTypes和queryContent来获取法律法规内容
    const typesRes = await getAllContentTypes()
    if (typesRes && (typesRes.code === 0 || typesRes.code === 1 || typesRes.code === 200) && typesRes.data) {
      const lawType = typesRes.data.find(type => type.contentTypeCode === 'law')
      if (lawType) {
        const params = {
          contentTypeId: lawType.contentTypeId,
          pageNum: 1,
          pageSize: 4,
          status: 1,
          deletedFlag: false
        }
        const result = await queryContent(params)
        if (result && result.data && result.data.list) {
          lawData.value = result.data.list.map(item => ({
            title: item.title,
            link: `/policy/detail/${item.contentId}`,
            date: formatDate(item.publishTime)
          }))
        } else {
          lawData.value = []
        }
      } else {
        lawData.value = []
      }
    } else {
      lawData.value = []
    }
  } catch (error) {
    console.error('获取法律法规数据失败:', error)
    lawData.value = []
  }
}

// 查询会员数据
const queryMemberData = async () => {
  try {
    // 使用getAllContentTypes和queryContent来获取会员内容
    const typesRes = await getAllContentTypes()
    if (typesRes && (typesRes.code === 0 || typesRes.code === 1 || typesRes.code === 200) && typesRes.data) {
      const memberType = typesRes.data.find(type => type.contentTypeCode === 'member')
      if (memberType) {
        const params = {
          contentTypeId: memberType.contentTypeId,
          pageNum: 1,
          pageSize: 6,
          status: 1,
          deletedFlag: false
        }
        const result = await queryContent(params)
        if (result && result.data && result.data.list) {
          memberData.value = result.data.list.map(item => ({
            name: item.title,
            logo: item.coverImage || memberLogo,
            description: item.summary || item.description || '单位会员'
          }))
        } else {
          memberData.value = []
        }
      } else {
        memberData.value = []
      }
    } else {
      memberData.value = []
    }
  } catch (error) {
    console.error('获取会员数据失败:', error)
    memberData.value = []
  }
}

// 查询服务中心数据
const queryServiceData = async () => {
  try {
    // 使用getAllContentTypes和queryContent来获取服务内容
    const typesRes = await getAllContentTypes()
    console.log('获取内容类型结果:', typesRes)
    
    if (typesRes && (typesRes.code === 0 || typesRes.code === 1 || typesRes.code === 200) && typesRes.data) {
      // 查找服务类型（使用精确的类型编码）
      const serviceType = typesRes.data.find(type => 
        type.contentTypeCode === 'service'
      )
      
      console.log('找到的服务类型:', serviceType)
      
      if (serviceType) {
        // 使用内容类型ID查询服务内容
        const params = {
          contentTypeId: serviceType.contentTypeId,
          pageNum: 1,
          pageSize: 6, // 首页显示6个服务
          status: 1, // 已发布状态
          deletedFlag: false // 未删除
        }
        
        console.log('查询服务参数:', params)
        
        const contentRes = await queryContent(params)
        console.log('获取服务结果:', contentRes)
        
        if (contentRes && (contentRes.code === 0 || contentRes.code === 1 || contentRes.code === 200) && contentRes.data && contentRes.data.list) {
          serviceData.value = contentRes.data.list.map(item => ({
            title: item.title,
            coverImage: item.coverImage,
            summary: item.summary,
            description: item.description,
            contentId: item.contentId
          }))
          console.log('获取到的服务数据:', serviceData.value)
        } else {
          console.warn('获取服务数据失败或无数据:', contentRes)
          serviceData.value = []
        }
      } else {
        console.warn('未找到service内容类型，请检查后台内容类型配置')
        serviceData.value = []
      }
    } else {
      console.warn('获取内容类型失败或内容类型数据为空:', typesRes)
      serviceData.value = []
    }
  } catch (error) {
    console.error('获取服务中心数据失败:', error)
    serviceData.value = []
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.home {
  width: 100%;
  position: relative;
  margin-top: 0;
  padding-top: 0;
}

.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0;
}

.main-content {
  display: flex;
  margin-bottom: 20px;
}

.left-column {
  flex: 1;
  margin-right: 20px;
}

.right-column {
  flex: 1;
}

.charity-section {
  margin-bottom: 20px;
}

.section-title {
  text-align: center;
  font-size: 24px;
  margin-bottom: 10px;
  color: #333;
}

.divider {
  width: 100px;
  height: 3px;
  background-color: var(--primary-color);
  margin: 0 auto 20px;
}

/* 服务中心样式 */
.service-section {
  margin: 60px 0;
  text-align: center;
}

.service-section .section-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 15px;
}

.service-section .divider {
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  margin: 0 auto 20px;
}

.service-section .section-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.service-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
}

.service-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-3px);
}

.service-card .card-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.service-card .card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-card .default-icon {
  font-size: 48px;
  color: #ccc;
}

.service-card .card-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-card .card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
}

.service-card .card-desc {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
  font-size: 14px;
  flex: 1;
}

.service-card .more-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
  align-self: flex-start;
}

.service-card .more-link:hover {
  color: var(--primary-dark-color);
  text-decoration: underline;
}

.service-more {
  text-align: center;
}

.more-btn {
  display: inline-block;
  padding: 12px 30px;
  background-color: var(--primary-color);
  color: #fff;
  text-decoration: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.more-btn:hover {
  background-color: var(--primary-dark-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .service-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .service-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .service-section {
    margin: 40px 0;
  }
  
  .service-section .section-title {
    font-size: 24px;
  }
  
  .service-section .section-description {
    font-size: 14px;
    margin-bottom: 30px;
  }
}
</style>