import{_ as S,O as _,c as a,h as n,a as c,b as f,d as b,i as r,t as d,F as h,j as k,f as g,l as v,D as A,s as j}from"./index-DIypJWEE.js";const z={class:"container"},I={class:"detail-wrapper"},L={key:0,class:"detail-content"},$={class:"detail-title"},O={key:0,class:"detail-meta"},P={key:0,class:"meta-item"},M={key:0,class:"meta-label"},W={class:"meta-value"},q={key:1,class:"meta-divider"},E={key:1,class:"info-section"},R={class:"info-table"},U={class:"info-label"},G={class:"info-value"},J={key:2,class:"detail-summary"},K={class:"summary-title"},Q={class:"summary-content"},X=["innerHTML"],Y={key:4,class:"detail-attachments"},Z={class:"attachments-list"},tt=["href"],et={class:"detail-actions"},at={key:1,class:"empty-data"},nt={class:"empty-actions"},it={__name:"ContentDetail",props:{detail:{type:Object,default:()=>({})},loading:{type:Boolean,default:!1},config:{type:Object,required:!0}},setup(t){const s=t,F=j(),D=_(()=>s.config.containerClass||"content-detail-container"),u=e=>{if(typeof e=="string")return s.detail[e];if(e.key){const i=e.key.split(".");let o=s.detail;for(const m of i)if(o&&o[m]!==void 0)o=o[m];else return null;return o}return null},p=(e,i)=>{if(!i)return"";if(e.type==="date"){if(typeof i=="string"&&i.includes(" "))return i.split(" ")[0];const o=new Date(i);if(!isNaN(o.getTime()))return o.toLocaleDateString("zh-CN")}if(e.type==="datetime"){const o=new Date(i);if(!isNaN(o.getTime()))return o.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}return e.formatter&&typeof e.formatter=="function"?e.formatter(i):i},B=()=>!s.config.infoSection||!s.config.infoSection.fields?!1:s.config.infoSection.fields.some(e=>u(e)),w=()=>{const e=C();return e&&e.length>0},C=()=>{if(!s.config.attachmentField)return[];const e=s.detail[s.config.attachmentField];return e?Array.isArray(e)?e:[e]:[]},H=(e,i)=>typeof e=="object"&&e.name?e.name:`附件${i+1}`,N=e=>{if(typeof e=="string")return e.startsWith("http")?e:`/api/files/${e}`;if(typeof e=="object"){if(e.url&&e.url.startsWith("http"))return e.url;if(e.url)return`/api/files/${e.url}`;if(e.path)return`/api/files/${e.path}`}return""},T=()=>{s.config.backHandler&&typeof s.config.backHandler=="function"?s.config.backHandler(F):F.go(-1)};return(e,i)=>{const o=g("a-button"),m=g("a-empty"),V=g("a-spin");return n(),a("div",{class:A(D.value)},[c("div",z,[c("div",I,[f(V,{spinning:t.loading,tip:"加载中..."},{default:b(()=>[t.detail&&t.detail[t.config.idField]?(n(),a("div",L,[c("h1",$,d(t.detail[t.config.titleField]),1),t.config.metaFields&&t.config.metaFields.length>0?(n(),a("div",O,[(n(!0),a(h,null,k(t.config.metaFields,(l,y)=>(n(),a(h,{key:l.key},[u(l)?(n(),a("span",P,[l.label?(n(),a("span",M,d(l.label)+"：",1)):r("",!0),c("span",W,d(p(l,u(l))),1)])):r("",!0),y<t.config.metaFields.length-1&&u(l)?(n(),a("span",q,"|")):r("",!0)],64))),128))])):r("",!0),t.config.infoSection&&B()?(n(),a("div",E,[c("div",R,[u(e.field)?(n(!0),a(h,{key:0},k(t.config.infoSection.fields,l=>(n(),a("div",{key:l.key,class:"info-row"},[c("div",U,d(l.label)+"：",1),c("div",G,d(p(l,u(l))),1)]))),128)):r("",!0)])])):r("",!0),t.config.summaryField&&t.detail[t.config.summaryField]?(n(),a("div",J,[c("div",K,d(t.config.summaryTitle||"摘要")+"：",1),c("div",Q,d(t.detail[t.config.summaryField]),1)])):r("",!0),t.config.contentField&&t.detail[t.config.contentField]?(n(),a("div",{key:3,class:"detail-content-body",innerHTML:t.detail[t.config.contentField]},null,8,X)):r("",!0),t.config.attachmentField&&w()?(n(),a("div",Y,[i[0]||(i[0]=c("div",{class:"attachments-title"},"附件：",-1)),c("div",Z,[(n(!0),a(h,null,k(C(),(l,y)=>(n(),a("div",{key:y,class:"attachment-item"},[c("a",{href:N(l),target:"_blank"},d(H(l,y)),9,tt)]))),128))])])):r("",!0),c("div",et,[f(o,{onClick:T},{default:b(()=>[v(d(t.config.backButtonText||"返回列表"),1)]),_:1})])])):t.loading?r("",!0):(n(),a("div",at,[f(m,{description:t.config.emptyText||"未找到内容"},null,8,["description"]),c("div",nt,[f(o,{onClick:T},{default:b(()=>[v(d(t.config.backButtonText||"返回列表"),1)]),_:1})])]))]),_:1},8,["spinning"])])])],2)}}},ft=S(it,[["__scopeId","data-v-7115419f"]]),x={idField:"contentId",titleField:"title",contentField:"contentHtml",summaryField:"summary",attachmentField:"attachment",containerClass:"news-detail-container",emptyText:"未找到新闻",backButtonText:"返回列表",metaFields:[{key:"contentTypeName",label:"分类"},{key:"author",label:"作者"},{key:"source",label:"来源"},{key:"publishTime",label:"发布时间",type:"date"},{key:"pageViewCount",label:"浏览量"}]},lt={idField:"id",titleField:"title",contentField:"contentHtml",summaryField:"summary",summaryTitle:"活动简介",attachmentField:"attachments",containerClass:"activity-detail-container",emptyText:"未找到活动信息",backButtonText:"返回列表",infoSection:{fields:[{key:"time",label:"活动时间"},{key:"location",label:"活动地点"},{key:"organizer",label:"主办单位"},{key:"contact",label:"联系方式"}]}},ot={idField:"id",titleField:"title",contentField:"contentHtml",attachmentField:"attachment",containerClass:"policy-detail-container",emptyText:"未找到政策信息",backButtonText:"返回政策法规",backHandler:t=>{t.push("/policy")},metaFields:[{key:"publishTime",label:"发布日期",type:"date"},{key:"source",label:"来源"},{key:"author",label:"作者"},{key:"pageViewCount",label:"浏览量",formatter:t=>t||0}]},ct={idField:"contentId",titleField:"title",contentField:"contentHtml",summaryField:"summary",attachmentField:"attachment",containerClass:"party-detail-container",emptyText:"未找到党建动态",backButtonText:"返回党建动态",backHandler:t=>{t.push("/party")},metaFields:[{key:"contentTypeName",label:"分类"},{key:"author",label:"作者"},{key:"source",label:"来源"},{key:"publishTime",label:"发布时间",type:"date"},{key:"pageViewCount",label:"浏览量"}]},st={idField:"id",titleField:"title",contentField:"content",containerClass:"representative-detail-page",emptyText:"暂无详细信息",backButtonText:"返回列表",metaFields:[{key:"publishDate",label:"发布时间",type:"date"},{key:"source",label:"来源"},{key:"author",label:"作者"},{key:"viewCount",label:"浏览量",formatter:t=>t||0}],infoSection:{fields:[{key:"director",label:"负责人"},{key:"establishTime",label:"成立时间"},{key:"contactPhone",label:"联系电话"},{key:"address",label:"办公地址",formatter:t=>t||"河南省郑州市金水区文化路97号"},{key:"email",label:"电子邮箱",formatter:t=>t||"<EMAIL>"}]}},rt={idField:"id",titleField:"title",contentField:"contentHtml",summaryField:"summary",attachmentField:"attachment",containerClass:"service-detail-container",emptyText:"未找到服务信息",backButtonText:"返回服务项目",metaFields:[{key:"publishTime",label:"发布时间",type:"date"},{key:"author",label:"作者"},{key:"source",label:"来源"},{key:"pageViewCount",label:"浏览量",formatter:t=>t||0}]},dt={idField:"id",titleField:"title",contentField:"contentHtml",attachmentField:"attachment",containerClass:"regulation-detail-container",emptyText:"未找到法规信息",backButtonText:"返回法规制度",metaFields:[{key:"publishTime",label:"发布时间",type:"date"},{key:"source",label:"来源"},{key:"author",label:"作者"},{key:"pageViewCount",label:"浏览量",formatter:t=>t||0}]},ut={idField:"branchId",titleField:"branchName",contentField:"description",containerClass:"branch-detail-container",emptyText:"未找到分会信息",backButtonText:"返回分会列表",infoSection:{fields:[{key:"establishTime",label:"成立时间"},{key:"memberCount",label:"会员数量"},{key:"contactPerson",label:"联系人"},{key:"contactPhone",label:"联系电话"},{key:"address",label:"地址"}]}},mt={idField:"id",titleField:"title",contentField:"contentHtml",summaryField:"summary",attachmentField:"attachment",containerClass:"branch-article-detail-container",emptyText:"未找到文章信息",backButtonText:"返回列表",metaFields:[{key:"publishTime",label:"发布时间",type:"date"},{key:"author",label:"作者"},{key:"source",label:"来源"},{key:"pageViewCount",label:"浏览量",formatter:t=>t||0}]},ht=t=>({news:x,activity:lt,policy:ot,party:ct,representative:st,service:rt,regulation:dt,branch:ut,branchArticle:mt})[t]||x;export{ft as C,dt as a,mt as b,ht as g,x as n,st as r};
