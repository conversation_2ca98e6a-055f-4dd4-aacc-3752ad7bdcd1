<template>
  <div class="activity-detail-container">
    <div class="container">
      <div class="detail-wrapper">
        <a-spin :spinning="loading" tip="加载中...">
          <div class="activity-detail" v-if="activityDetail.id">
            <h1 class="activity-title">{{ activityDetail.title }}</h1>

            <div class="activity-meta">
              <div class="meta-item">
                <span class="meta-label">活动时间：</span>
                <span class="meta-value">{{ activityDetail.time }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">活动地点：</span>
                <span class="meta-value">{{ activityDetail.location }}</span>
              </div>
              <div class="meta-item" v-if="activityDetail.organizer">
                <span class="meta-label">主办单位：</span>
                <span class="meta-value">{{ activityDetail.organizer }}</span>
              </div>
              <div class="meta-item" v-if="activityDetail.contact">
                <span class="meta-label">联系方式：</span>
                <span class="meta-value">{{ activityDetail.contact }}</span>
              </div>
            </div>

            <div class="activity-summary" v-if="activityDetail.summary">
              <div class="summary-title">活动简介：</div>
              <div class="summary-content">{{ activityDetail.summary }}</div>
            </div>

            <div class="activity-content" v-html="activityDetail.contentHtml"></div>

            <div class="activity-attachments" v-if="activityDetail.attachments && activityDetail.attachments.length > 0">
              <div class="attachments-title">附件：</div>
              <div class="attachments-list">
                <div v-for="(item, index) in activityDetail.attachments" :key="index" class="attachment-item">
                  <a :href="getFileUrl(item)" target="_blank">附件{{ index + 1 }}</a>
                </div>
              </div>
            </div>

            <div class="activity-actions">
              <a-button @click="goBack">返回列表</a-button>
            </div>
          </div>

          <div class="empty-data" v-else-if="!loading">
            <a-empty description="未找到活动信息" />
            <div class="empty-actions">
              <a-button @click="goBack">返回列表</a-button>
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getContentDetail } from '../../api/content'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const activityDetail = ref({})

// 获取活动详情
const fetchActivityDetail = async () => {
  try {
    loading.value = true
    const id = route.params.id
    if (!id) {
      message.error('缺少活动ID参数')
      return
    }

    const res = await getContentDetail(id)
    if (res && (res.code === 0 || res.code === 1 || res.code === 200) && res.data) {
      // 转换数据格式以适配现有模板
      activityDetail.value = {
        id: res.data.contentId,
        title: res.data.title,
        time: formatDate(res.data.publishTime || res.data.createTime),
        location: res.data.summary || '待定',
        organizer: '河南省软组织病研究会',
        contact: '0371-12345678',
        summary: res.data.summary,
        contentHtml: res.data.contentHtml || res.data.description,
        attachments: res.data.attachments || []
      }
    } else {
      message.error('获取活动详情失败')
    }
  } catch (error) {
    console.error('获取活动详情失败:', error)
    message.error('获取活动详情失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取文件URL
const getFileUrl = (filePath) => {
  if (!filePath) return ''
  if (filePath.startsWith('http')) {
    return filePath
  }
  return `${import.meta.env.VITE_API_BASE_URL || '/api'}/files/${filePath}`
}

// 返回列表
const goBack = () => {
  router.go(-1)
}

// 页面加载时获取活动详情
onMounted(() => {
  fetchActivityDetail()
})
</script>

<style scoped>
.activity-detail-container {
  background-color: #f5f5f5;
  min-height: calc(100vh - 120px);
  overflow-x: hidden; /* 防止水平滚动条 */
}

.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0 40px;
  overflow-x: hidden; /* 防止水平滚动条 */
}

.detail-wrapper {
  min-height: 500px;
  position: relative;
  overflow: hidden; /* 防止内容溢出 */
}

.activity-detail {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box; /* 确保padding不会增加宽度 */
  min-height: 400px; /* 确保最小高度 */
}

.activity-title {
  font-size: 28px;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.activity-meta {
  background-color: #f8f8f8;
  padding: 15px 20px;
  margin-bottom: 30px;
  border-radius: 4px;
  border-left: 4px solid var(--primary-color);
}

.meta-item {
  margin-bottom: 10px;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-weight: bold;
  color: #333;
  margin-right: 5px;
}

.meta-value {
  color: #666;
}

.activity-summary {
  background-color: #f8f8f8;
  padding: 15px 20px;
  margin-bottom: 30px;
  border-radius: 4px;
  border-left: 4px solid var(--primary-color);
}

.summary-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.summary-content {
  color: #666;
  line-height: 1.6;
}

.activity-content {
  line-height: 1.8;
  color: #333;
  margin-bottom: 30px;
}

.activity-attachments {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px dashed #eee;
}

.attachments-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.attachment-item {
  margin-bottom: 8px;
}

.attachment-item a {
  color: #1890ff;
}

.activity-actions {
  margin-top: 30px;
  text-align: center;
}

.empty-data {
  padding: 60px 0;
  text-align: center;
}

.empty-actions {
  margin-top: 20px;
}
</style>

<style>
.activity-content img {
  max-width: 100%;
  height: auto;
}

.activity-content p {
  margin-bottom: 16px;
}

.activity-content h1, .activity-content h2, .activity-content h3, .activity-content h4, .activity-content h5 {
  margin-top: 24px;
  margin-bottom: 16px;
}

.activity-content ul, .activity-content ol {
  padding-left: 20px;
  margin-bottom: 16px;
}

.activity-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 16px;
}

.activity-content table, .activity-content th, .activity-content td {
  border: 1px solid #ddd;
}

.activity-content th, .activity-content td {
  padding: 8px;
  text-align: left;
}
</style>
