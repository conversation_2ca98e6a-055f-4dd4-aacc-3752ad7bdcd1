<template>
  <div class="app-header">
    <div class="top-header">
      <div class="container">
        <div class="social-code">统一社会信用代码：{{ websiteConfig.socialCreditCode || 'xxxxxxxxxxxxxxxx' }}</div>
        <div class="quick-links">
          <a href="/">返回首页</a> |
          <a href="javascript:void(0)" @click="setHomePage">设为首页</a> |
          <a href="javascript:void(0)" @click="addFavorite">加入收藏</a>
        </div>
      </div>
    </div>

    <div class="main-header container">
      <div class="logo">
        <img :src="websiteConfig.websiteLogo || defaultLogo" :alt="websiteConfig.organizationName || '河南省软组织病研究会'" />
        <div class="logo-text">
          <h1>{{ websiteConfig.organizationName || '河南省软组织病研究会' }}</h1>
          <p>{{ websiteConfig.organizationEnglishName || 'Henan Association for Soft Tissue Disease Research' }}</p>
        </div>
      </div>

      <div class="search-box">
        <a-input-search
          placeholder="请输入搜索关键词..."
          style="width: 250px"
          @search="onSearch"
        >
          <template #enterButton>
            <a-button type="primary" :style="{ backgroundColor: themeConfig?.primaryColor || 'var(--primary-color)', borderColor: themeConfig?.primaryColor || 'var(--primary-color)' }">搜索</a-button>
          </template>
        </a-input-search>
      </div>
    </div>

    <div class="main-nav">
      <div class="container">
        <!-- 菜单加载状态 -->
        <div v-if="menuLoading" class="menu-loading">
          <a-spin size="small" />
          <span style="margin-left: 8px;">加载菜单中...</span>
        </div>

        <!-- 动态菜单 -->
        <a-menu
          v-else
          mode="horizontal"
          theme="dark"
          v-model:selectedKeys="selectedKeys"
        >
          <!-- 动态菜单项 -->
          <template v-for="menu in topMenuList" :key="menu.menuId">
            <!-- 如果是菜单类型，直接显示 -->
            <a-menu-item
              v-if="menu.menuType === 2 && menu.path"
              :key="getMenuKey(menu)"
            >
              <router-link v-if="menu.path" :to="menu.path">{{ menu.menuName }}</router-link>
              <span v-else>{{ menu.menuName }}</span>
            </a-menu-item>

            <!-- 如果是目录类型，显示下拉菜单 -->
            <a-sub-menu
              v-else-if="menu.menuType === 1 || getChildrenMenus(menu.menuId).length > 0"
              :key="'sub-' + menu.menuId"
            >
              <template #title>{{ menu.menuName }}</template>
              <a-menu-item
                v-for="subMenu in getChildrenMenus(menu.menuId)"
                :key="getMenuKey(subMenu, true)"
              >
                <router-link v-if="subMenu.path" :to="subMenu.path">{{ subMenu.menuName }}</router-link>
                <span v-else>{{ subMenu.menuName }}</span>
              </a-menu-item>
            </a-sub-menu>

            <!-- 如果是菜单类型但没有路径，也显示为普通菜单项 -->
            <a-menu-item
              v-else
              :key="'menu-' + menu.menuId"
            >
              <span>{{ menu.menuName }}</span>
            </a-menu-item>
          </template>

          <!-- 备用静态菜单 - 当动态菜单加载失败或为空时显示 -->
          <template v-if="!menuLoading && topMenuList.length === 0">
            <a-menu-item key="home">
              <router-link to="/">首页</router-link>
            </a-menu-item>
            <a-menu-item key="news">
              <router-link to="/news">新闻资讯</router-link>
            </a-menu-item>
            <a-menu-item key="academic">
              <router-link to="/academic">学术活动</router-link>
            </a-menu-item>
            <a-menu-item key="party">
              <router-link to="/party">党建工作</router-link>
            </a-menu-item>
            <a-menu-item key="activity">
              <router-link to="/activity">活动中心</router-link>
            </a-menu-item>
            <a-menu-item key="info">
              <router-link to="/info">信息公开</router-link>
            </a-menu-item>
            <a-menu-item key="service">
              <router-link to="/service">会员服务</router-link>
            </a-menu-item>
            <a-menu-item key="join">
              <router-link to="/join">加入我们</router-link>
            </a-menu-item>
          </template>
        </a-menu>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '../../store/modules/appStore'
import defaultLogo from '../../assets/images/logo.svg'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

// 获取网站配置
const websiteConfig = computed(() => appStore.getWebsiteConfig)
// 获取主题配置
const themeConfig = computed(() => appStore.getThemeConfig)

// 从 store 中获取菜单数据
const topMenuList = computed(() => {
  return appStore.getTopMenuList
})

// 获取菜单加载状态
const menuLoading = computed(() => appStore.menuLoading)

// 获取指定父菜单ID下的子菜单
const getChildrenMenus = (parentId) => {
  return appStore.getChildrenMenus(parentId)
}

// 生成唯一的菜单项 key
const getMenuKey = (menu, isSubMenu = false) => {
  // 如果是首页菜单
  if (menu.path === '/' || menu.menuName === '首页') {
    return 'home'
  }

  // 如果有路径，使用路径作为 key
  if (menu.path) {
    const pathKey = menu.path.replace(/^\//, '')
    return isSubMenu ? `sub-${pathKey}` : pathKey
  }

  // 如果没有路径，使用菜单ID作为 key
  return isSubMenu ? `sub-${menu.menuId}` : `menu-${menu.menuId}`
}

// 选中的菜单项
const selectedKeys = ref([])

// 更新选中的菜单项
const updateSelectedKeys = () => {
  const path = route.path

  if (path === '/') {
    selectedKeys.value = ['home']
  } else {
    // 获取路径的第一段作为菜单key
    const mainPath = path.split('/')[1]

    // 查找匹配的菜单项
    let matchedMenu = topMenuList.value.find(menu => {
      if (menu.path && menu.path.startsWith('/' + mainPath)) {
        return true
      }
      return false
    })

    // 如果没有找到直接匹配的菜单项，尝试查找详情页面对应的主菜单
    if (!matchedMenu && path.includes('/detail/')) {
      const basePath = '/' + mainPath
      matchedMenu = topMenuList.value.find(menu => {
        return menu.path === basePath
      })
    }

    if (matchedMenu) {
      selectedKeys.value = [getMenuKey(matchedMenu)]
    } else {
      selectedKeys.value = [mainPath]
    }
  }
}

// 监听路由变化，更新选中的菜单
watch(() => route.path, () => {
  updateSelectedKeys()
}, { immediate: true })

// 监听菜单数据变化
watch(() => topMenuList.value, () => {
  updateSelectedKeys()
}, { immediate: true })

// 组件挂载时初始化
onMounted(() => {
  // 如果菜单数据还没有加载，尝试加载
  if (!appStore.menuLoaded && !appStore.menuLoading) {
    appStore.loadMenuList()
  }
})

const setHomePage = () => {
  alert('设为首页操作需要浏览器支持，请手动操作。')
}

const addFavorite = () => {
  try {
    alert('请按 Ctrl+D (或 Command+D) 添加到收藏夹')
  } catch (e) {
    alert('您的浏览器不支持自动添加收藏，请手动操作。')
  }
}

const onSearch = (value) => {
  console.log('搜索:', value)
}
</script>

<style scoped>
.app-header {
  width: 100%;
  position: relative;
  z-index: 100;
}

.top-header {
  background-color: #f5f5f5;
  padding: 5px 0;
  font-size: 12px;
  border-bottom: 1px solid #eaeaea;
}

.container {
  width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-links a {
  color: #666;
  text-decoration: none;
  margin: 0 5px;
}

.main-header {
  padding: 15px 0;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 80px;
  margin-right: 15px;
}

.logo-text h1 {
  font-size: 28px;
  color: var(--primary-color);
  margin: 0;
  line-height: 1.2;
}

.logo-text p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 菜单加载状态样式 */
.menu-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  color: white;
  font-size: 14px;
}

/* 主导航样式 */
.main-nav {
  background-color: var(--primary-color);
  height: 50px;
  overflow: visible;
  position: relative;
  z-index: 100;
}

/* 基础菜单样式 */
.main-nav :deep(.ant-menu) {
  background-color: transparent;
  border: none;
  height: 50px;
  line-height: 50px;
}

.main-nav :deep(.ant-menu-horizontal) {
  border-bottom: none;
}

.main-nav :deep(.ant-menu-horizontal > .ant-menu-item),
.main-nav :deep(.ant-menu-horizontal > .ant-menu-submenu) {
  color: white;
  font-size: 16px;
  height: 50px;
  line-height: 50px;
  border-bottom: none;
}

.main-nav :deep(.ant-menu-item:hover),
.main-nav :deep(.ant-menu-item-selected),
.main-nav :deep(.ant-menu-submenu:hover),
.main-nav :deep(.ant-menu-submenu-selected) {
  color: white !important;
  background-color: var(--primary-dark-color);
  border-bottom: none;
}

.main-nav :deep(.ant-menu-submenu-title) {
  color: white;
}

.main-nav :deep(.ant-menu-item a),
.main-nav :deep(.ant-menu-submenu a) {
  color: white;
}

.main-nav :deep(.ant-menu-item:hover a),
.main-nav :deep(.ant-menu-submenu:hover a),
.main-nav :deep(.ant-menu-item-selected a),
.main-nav :deep(.ant-menu-submenu-selected a) {
  color: white !important;
}

/* 子菜单样式 */
.main-nav :deep(.ant-menu-sub) {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.main-nav :deep(.ant-menu-sub .ant-menu-item) {
  color: white !important;
}

.main-nav :deep(.ant-menu-sub .ant-menu-item:hover) {
  background-color: var(--primary-dark-color) !important;
  color: white !important;
}

.main-nav :deep(.ant-menu-sub .ant-menu-item a) {
  color: white !important;
}

.main-nav :deep(.ant-menu-sub .ant-menu-item:hover a) {
  color: white !important;
}

/* 移除下拉箭头 */
.main-nav :deep(.ant-menu-submenu-title)::after {
  display: none !important;
}
</style>