import{b as i,L as y,_ as O,o as g,c as f,a as t,d as r,f as d,h as b,l as c,t as o,x as L,i as m}from"./index-DIypJWEE.js";import{I as N}from"./AntdIcon-C5sZj2-2.js";function v(l){for(var s=1;s<arguments.length;s++){var a=arguments[s]!=null?Object(arguments[s]):{},n=Object.keys(a);typeof Object.getOwnPropertySymbols=="function"&&(n=n.concat(Object.getOwnPropertySymbols(a).filter(function(e){return Object.getOwnPropertyDescriptor(a,e).enumerable}))),n.forEach(function(e){k(l,e,a[e])})}return l}function k(l,s,a){return s in l?Object.defineProperty(l,s,{value:a,enumerable:!0,configurable:!0,writable:!0}):l[s]=a,l}var _=function(s,a){var n=v({},s,a.attrs);return i(N,v({},n,{icon:y}),null)};_.displayName="LeftOutlined";_.inheritAttrs=!1;const P={class:"branch-detail-container"},B={class:"container"},C={class:"breadcrumb"},T={class:"detail-wrapper"},j={class:"action-bar"},V={class:"detail-content"},E={class:"detail-title"},I={class:"detail-meta"},M={key:0,class:"meta-divider"},H={key:1,class:"detail-tag"},S={class:"info-section"},Y={class:"info-table"},A={class:"info-row"},$={class:"info-value"},q={class:"info-row"},z={class:"info-value"},F={class:"info-row"},G={class:"info-value"},J={class:"info-row"},Q={class:"info-value"},R={class:"info-row"},U={class:"info-value"},W={class:"content-section"},X=["innerHTML"],Z={__name:"detail",setup(l){const s=n=>n.includes(" ")?n.split(" ")[0]:n,a=()=>{router.replace("/branch"),setTimeout(()=>{document.documentElement.style.overflowY="scroll",document.body.style.overflowY="scroll"},100)};return g(()=>{fetchBranchDetail()}),(n,e)=>{const p=d("router-link"),u=d("a-breadcrumb-item"),h=d("a-breadcrumb"),D=d("a-button"),w=d("a-spin");return b(),f("div",P,[t("div",B,[t("div",C,[i(h,null,{default:r(()=>[i(u,null,{default:r(()=>[i(p,{to:"/"},{default:r(()=>e[0]||(e[0]=[c("首页")])),_:1})]),_:1}),i(u,null,{default:r(()=>[i(p,{to:"/branch"},{default:r(()=>e[1]||(e[1]=[c("分会")])),_:1})]),_:1}),i(u,null,{default:r(()=>[c(o(n.branchDetail.name),1)]),_:1})]),_:1})]),t("div",T,[t("div",j,[i(D,{onClick:a,type:"primary"},{icon:r(()=>[i(L(_))]),default:r(()=>[e[2]||(e[2]=c(" 返回列表 "))]),_:1})]),i(w,{spinning:n.loading,tip:"加载中..."},{default:r(()=>[t("div",V,[t("h1",E,o(n.branchDetail.name),1),t("div",I,[t("span",null,"发布时间："+o(s(n.branchDetail.publishDate)),1),e[3]||(e[3]=t("span",{class:"meta-divider"},"|",-1)),t("span",null,"来源："+o(n.branchDetail.source),1),e[4]||(e[4]=t("span",{class:"meta-divider"},"|",-1)),t("span",null,"作者："+o(n.branchDetail.author),1),e[5]||(e[5]=t("span",{class:"meta-divider"},"|",-1)),t("span",null,"浏览量："+o(n.branchDetail.viewCount||0),1),n.branchDetail.isNew?(b(),f("span",M,"|")):m("",!0),n.branchDetail.isNew?(b(),f("span",H,"新成立")):m("",!0)]),t("div",S,[t("div",Y,[t("div",A,[e[6]||(e[6]=t("div",{class:"info-label"},"负责人：",-1)),t("div",$,o(n.branchDetail.director),1)]),t("div",q,[e[7]||(e[7]=t("div",{class:"info-label"},"成立时间：",-1)),t("div",z,o(n.branchDetail.establishTime),1)]),t("div",F,[e[8]||(e[8]=t("div",{class:"info-label"},"联系电话：",-1)),t("div",G,o(n.branchDetail.contactPhone),1)]),t("div",J,[e[9]||(e[9]=t("div",{class:"info-label"},"办公地址：",-1)),t("div",Q,o(n.branchDetail.address),1)]),t("div",R,[e[10]||(e[10]=t("div",{class:"info-label"},"电子邮箱：",-1)),t("div",U,o(n.branchDetail.email),1)])])]),t("div",W,[t("div",{class:"content-text",innerHTML:n.branchDetail.content},null,8,X)])])]),_:1},8,["spinning"])])])])}}},tt=O(Z,[["__scopeId","data-v-a958a6d5"]]);export{tt as default};
