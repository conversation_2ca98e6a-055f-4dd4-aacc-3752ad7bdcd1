import{_ as E,r as i,K as J,o as Q,e as r,s as $,T as K,c as f,P as C,a,b as u,d as p,t as n,i as X,D as N,n as F,x as O,f as g,h as v,l as _}from"./index-DIypJWEE.js";import{a as G,v as R,b as H}from"./registration-Dy1-3mUd.js";const Y="/soft-henan/logo-association.png",Z={class:"payment-container"},ee={class:"main-content"},ae={class:"order-countdown"},te={class:"countdown-header"},se={class:"countdown-title"},oe={class:"time-remaining"},ne={class:"countdown-desc"},ie={class:"registration-info-card"},re={class:"card-content"},de={class:"order-summary"},le={class:"order-row"},ce={class:"order-value"},ve={class:"order-row"},ue={class:"order-value"},pe={class:"order-row"},me={class:"order-value"},ye={class:"order-row"},fe={class:"order-value highlight"},ge={class:"order-row"},_e={class:"order-value"},we={class:"order-row"},he={class:"order-value"},Ie={class:"order-row"},Se={class:"order-value order-id"},be={class:"order-row total-row"},ke={class:"order-value total-fee"},Ce={class:"payment-methods-card"},Xe={class:"card-content"},Pe={class:"payment-options"},xe={key:0,class:"payment-content"},De={key:1,class:"qrcode-container"},qe={class:"qrcode-header"},Be={class:"qrcode-wrapper"},Je=["src","alt"],Ne={class:"qrcode-tips"},Fe={class:"scan-tip"},Oe={class:"payment-amount"},Re={class:"payment-notice"},Te={class:"payment-verifying"},Ve={__name:"payment",setup(We){const w=$(),o=i(""),P=navigator.userAgent.toLowerCase().includes("micromessenger"),s=J({}),T=J({}),h=i(""),I=i(!1),S=i(!1),m=i(!1),d=i(null),b=i(!1),V=i(30),y=i(V.value*60),l=i(null),x=i("30:00");Q(()=>{const t=localStorage.getItem("registrationData");t&&Object.assign(s,JSON.parse(t));const e=localStorage.getItem("paymentData");e&&Object.assign(T,JSON.parse(e)),s.orderId||(r.error("订单信息不完整，请重新填写"),w.push("/registration")),W()}),K(()=>{d.value&&clearInterval(d.value),l.value&&clearInterval(l.value)});const W=()=>{l.value&&clearInterval(l.value),l.value=setInterval(()=>{y.value-=1;const t=Math.floor(y.value/60),e=y.value%60;x.value=`${t.toString().padStart(2,"0")}:${e.toString().padStart(2,"0")}`,y.value<=0&&(clearInterval(l.value),r.error("订单已超时，请重新报名"),setTimeout(()=>{w.push("/registration")},2e3))},1e3)},D=async()=>{if(o.value){I.value=!0,h.value="";try{const t={orderId:s.orderId,paymentType:o.value,amount:s.totalFee},e=await G(t);e&&e.code===1&&e.data&&e.data.qrCodeUrl?(h.value=e.data.qrCodeUrl,M()):r.error("获取支付二维码失败")}catch(t){console.error("获取支付二维码失败",t),r.error("获取支付二维码失败，请稍后重试")}finally{I.value=!1}}},M=()=>{d.value&&clearInterval(d.value),d.value=setInterval(async()=>{try{const t=await R(s.orderId);t&&t.code===1&&t.data&&t.data.paymentStatus==="PAID"&&(clearInterval(d.value),k())}catch(t){console.error("检查支付状态失败",t)}},1e4)},U=async()=>{if(!o.value){r.warning("请选择支付方式");return}S.value=!0,m.value=!0;try{const t=await R(s.orderId);t&&t.code===1&&t.data&&t.data.paymentStatus==="PAID"?k():r.warning("未检测到支付完成，请完成支付后再试")}catch(t){console.error("验证支付状态失败",t),r.error("验证支付状态失败，请稍后重试")}finally{S.value=!1,m.value=!1}},k=()=>{localStorage.setItem("paymentData",JSON.stringify({method:o.value,amount:s.totalFee,time:new Date().toISOString(),orderId:s.orderId})),r.success("支付成功！"),d.value&&clearInterval(d.value),w.push("/registration/complete")};async function j(){if(o.value){b.value=!0;try{const t={orderId:s.orderId,amount:s.totalFee},e=await H(t);if(e&&e.code===1&&e.data){const c={appId:e.data.appId,timeStamp:e.data.timeStamp,nonceStr:e.data.nonceStr,package:e.data.package,signType:e.data.signType,paySign:e.data.paySign};A(c)}else r.error("生成微信支付参数失败")}catch(t){console.error("JSAPI 支付参数生成失败",t),r.error("生成微信支付参数失败")}finally{b.value=!1}}}function A(t){function e(){WeixinJSBridge.invoke("getBrandWCPayRequest",t,function(c){c.err_msg==="get_brand_wcpay_request:ok"?k():r.error("支付取消或失败")})}typeof WeixinJSBridge>"u"?document.addEventListener("WeixinJSBridgeReady",e,!1):e()}return(t,e)=>{const c=g("a-alert"),q=g("a-button"),B=g("a-spin"),z=g("a-modal");return v(),f("div",Z,[e[23]||(e[23]=C('<div class="payment-banner" data-v-e0e23c51><div class="banner-content" data-v-e0e23c51><div class="logo-wrapper" data-v-e0e23c51><img src="'+Y+'" alt="河南省软组织病研究会" class="association-logo" data-v-e0e23c51></div><h1 data-v-e0e23c51>参会费用支付</h1><p class="subtitle" data-v-e0e23c51>河南省软组织病研究会学术交流会议</p></div></div>',1)),a("div",ee,[e[21]||(e[21]=C('<div class="process-steps" data-v-e0e23c51><div class="step completed" data-v-e0e23c51><div class="step-icon" data-v-e0e23c51>1</div><div class="step-text" data-v-e0e23c51>填写报名</div></div><div class="step-line" data-v-e0e23c51></div><div class="step active" data-v-e0e23c51><div class="step-icon" data-v-e0e23c51>2</div><div class="step-text" data-v-e0e23c51>支付费用</div></div><div class="step-line" data-v-e0e23c51></div><div class="step" data-v-e0e23c51><div class="step-icon" data-v-e0e23c51>3</div><div class="step-text" data-v-e0e23c51>报名成功</div></div></div>',1)),a("div",ae,[a("div",te,[u(c,{type:"warning","show-icon":""},{message:p(()=>[a("span",se,[e[3]||(e[3]=_("请在 ")),a("span",oe,n(x.value),1),e[4]||(e[4]=_(" 内完成支付"))])]),description:p(()=>[a("div",ne,"订单号："+n(s.orderId),1)]),_:1})])]),a("div",ie,[e[15]||(e[15]=a("div",{class:"card-header"},[a("i",{class:"info-icon"}),a("span",null,"报名信息")],-1)),a("div",re,[a("div",de,[a("div",le,[e[5]||(e[5]=a("div",{class:"order-label"},"姓名",-1)),a("div",ce,n(s.name),1)]),a("div",ve,[e[6]||(e[6]=a("div",{class:"order-label"},"手机号码",-1)),a("div",ue,n(s.phone),1)]),a("div",pe,[e[7]||(e[7]=a("div",{class:"order-label"},"医院/单位",-1)),a("div",me,n(s.hospital),1)]),e[13]||(e[13]=a("div",{class:"divider"},null,-1)),a("div",ye,[e[8]||(e[8]=a("div",{class:"order-label"},"参会人数",-1)),a("div",fe,n(s.attendees)+"人",1)]),a("div",ge,[e[9]||(e[9]=a("div",{class:"order-label"},"参会时间",-1)),a("div",_e,n(s.time),1)]),a("div",we,[e[10]||(e[10]=a("div",{class:"order-label"},"晚宴",-1)),a("div",he,n(s.dinner?"参加":"不参加"),1)]),e[14]||(e[14]=a("div",{class:"divider"},null,-1)),a("div",Ie,[e[11]||(e[11]=a("div",{class:"order-label"},"订单号",-1)),a("div",Se,n(s.orderId),1)]),a("div",be,[e[12]||(e[12]=a("div",{class:"order-label"},"应付金额",-1)),a("div",ke,"¥"+n(s.totalFee.toFixed(2)),1)])])])]),a("div",Ce,[e[20]||(e[20]=a("div",{class:"card-header"},[a("i",{class:"payment-header-icon"}),a("span",null,"选择支付方式")],-1)),a("div",Xe,[a("div",Pe,[a("div",{class:N(["payment-option",{active:o.value==="wechat"}]),onClick:e[0]||(e[0]=()=>{o.value="wechat",D()})},e[16]||(e[16]=[a("div",{class:"option-icon wechat-icon"},null,-1),a("div",{class:"option-name"},"微信支付",-1)]),2),a("div",{class:N(["payment-option",{active:o.value==="alipay"}]),onClick:e[1]||(e[1]=()=>{o.value="alipay",D()})},e[17]||(e[17]=[a("div",{class:"option-icon alipay-icon"},null,-1),a("div",{class:"option-name"},"支付宝",-1)]),2)]),o.value?(v(),f("div",xe,[o.value==="wechat"&&O(P)?(v(),F(q,{key:0,type:"primary",onClick:j,loading:b.value,size:"large",block:"",class:"jsapi-button"},{default:p(()=>e[18]||(e[18]=[a("span",{class:"button-icon wechat-icon"},null,-1),_(" 一键微信支付 ")])),_:1},8,["loading"])):X("",!0),o.value&&(!O(P)||o.value==="alipay")?(v(),f("div",De,[a("div",qe,n(o.value==="wechat"?"微信":"支付宝")+"扫码支付 ",1),a("div",Be,[I.value?(v(),F(B,{key:0,tip:"生成支付二维码..."})):(v(),f("img",{key:1,src:h.value,alt:o.value==="wechat"?"微信支付二维码":"支付宝支付二维码"},null,8,Je))]),a("div",Ne,[a("p",Fe,"请使用"+n(o.value==="wechat"?"微信":"支付宝")+"扫一扫",1),a("p",Oe,"¥"+n(s.totalFee.toFixed(2)),1)]),u(q,{type:"primary",onClick:U,loading:S.value,size:"large",block:""},{default:p(()=>e[19]||(e[19]=[_(" 确认已支付 ")])),_:1},8,["loading"])])):X("",!0)])):X("",!0)])]),a("div",Re,[u(c,{type:"info","show-icon":"",message:"支付须知",description:"请在30分钟内完成支付，超时订单将自动取消。支付成功后将收到确认短信，如有问题请联系会务组。"})])]),e[24]||(e[24]=C('<div class="footer-info" data-v-e0e23c51><div class="info-item" data-v-e0e23c51><i class="location-icon" data-v-e0e23c51></i><span data-v-e0e23c51><strong data-v-e0e23c51>会议地点：</strong>郑州市XXXX酒店（XXXX路XX号）</span></div><div class="info-item" data-v-e0e23c51><i class="phone-icon" data-v-e0e23c51></i><span data-v-e0e23c51><strong data-v-e0e23c51>联系方式：</strong>0371-XXXXXXXX（赵老师）</span></div><div class="info-item" data-v-e0e23c51><i class="org-icon" data-v-e0e23c51></i><span data-v-e0e23c51><strong data-v-e0e23c51>主办单位：</strong>河南省软组织病研究会</span></div></div>',1)),u(z,{visible:m.value,"onUpdate:visible":e[2]||(e[2]=L=>m.value=L),title:"支付验证中",closable:!1,maskClosable:!1,footer:null},{default:p(()=>[a("div",Te,[u(B),e[22]||(e[22]=a("p",null,"正在验证支付状态，请稍候...",-1))])]),_:1},8,["visible"])])}}},je=E(Ve,[["__scopeId","data-v-e0e23c51"]]);export{je as default};
