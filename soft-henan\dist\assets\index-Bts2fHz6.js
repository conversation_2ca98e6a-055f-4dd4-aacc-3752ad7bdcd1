import{m as j,q as E,p as H,C as U,_ as Y,r as p,o as G,c as h,a as l,D,l as I,F as L,j as S,b as k,i as b,t as v,d as J,e as $,g as K,f as W,s as O,h as m}from"./index-DIypJWEE.js";function Q(a){const e=a.split("||"),o={director:"张明",establishTime:"",contactPhone:"0371-XXXXXXXX",address:"河南省郑州市金水区文化路97号河南省人民医院",email:"<EMAIL>"};return e.forEach(r=>{var u,d,f,g,_;const t=r.trim();if(t.startsWith("负责人:")||t.startsWith("负责人：")||t.startsWith("主任委员:")||t.startsWith("主任委员：")){const i=(u=t.split(/[：:]/)[1])==null?void 0:u.trim();i&&(o.director=i)}else if(t.startsWith("成立时间:")||t.startsWith("成立时间：")||t.includes("成立于")){let i="";if(t.includes("成立于")){const y=t.match(/成立于\s*([0-9]{4}[年\s\./-][0-9]{1,2}[月]?)/);i=y?y[1]:""}else i=(d=t.split(/[：:]/)[1])==null?void 0:d.trim();i&&(o.establishTime=i)}else if(t.startsWith("电话:")||t.startsWith("电话：")||t.startsWith("联系电话:")||t.startsWith("联系电话：")){const i=(f=t.split(/[：:]/)[1])==null?void 0:f.trim();i&&(o.contactPhone=i)}else if(t.startsWith("地址:")||t.startsWith("地址：")||t.startsWith("办公地址:")||t.startsWith("办公地址：")){const i=(g=t.split(/[：:]/)[1])==null?void 0:g.trim();i&&(o.address=i)}else if(t.startsWith("邮箱:")||t.startsWith("邮箱：")||t.startsWith("电子邮箱:")||t.startsWith("电子邮箱：")){const i=(_=t.split(/[：:]/)[1])==null?void 0:_.trim();i&&(o.email=i)}}),o}function X(a={}){return j().then(e=>{if(console.log("获取内容类型结果:",e),e&&e.data){const o=e.data.find(r=>r.contentTypeCode==="branch"||r.contentTypeName==="专业委员会"||r.contentTypeName==="分会");if(console.log("找到的分会类型:",o),o){const r={pageNum:1,pageSize:50,status:1,deletedFlag:!1,...a,contentTypeId:o.contentTypeId,pageNum:Number(a.pageNum||1),pageSize:Number(a.pageSize||50)};return console.log("查询分会内容参数:",r),E(r).then(t=>{if(console.log("获取分会列表结果:",t),t&&t.data&&t.data.list){const u=t.data.list.map(d=>({id:d.contentId,name:d.title,isNew:d.topFlag||!1,description:d.summary||"",createTime:d.createTime}));return{code:t.code,data:{list:u,total:t.data.total||u.length},msg:t.msg}}return N()}).catch(t=>(console.error("查询分会内容失败:",t),N()))}}return console.log("未找到分会类型，返回默认数据"),N()}).catch(e=>(console.error("获取内容类型失败:",e),N()))}function N(){return{code:1,data:[{id:1,name:"标准化工作分会",isNew:!0},{id:2,name:"针刀医学分会",isNew:!1},{id:3,name:"逆袭糖尿病分会",isNew:!1},{id:4,name:"传承创新发展工作分会",isNew:!1},{id:5,name:"中医理疗分会",isNew:!1},{id:6,name:"基层服务能力与医师诊疗能力双提升分会",isNew:!0},{id:7,name:"天灸肺病防治学分会",isNew:!1}],msg:"操作成功(默认数据)"}}function Z(a){return H(a).then(e=>{var o;if(console.log("获取分会详情结果:",e),e&&(e.code===0||e.code===1)&&e.data){const r=Q(e.data.summary||"");return{code:e.code,data:{id:e.data.contentId,name:e.data.title,isNew:e.data.topFlag||!1,description:e.data.summary||"",content:e.data.contentHtml,createTime:e.data.createTime,updateTime:e.data.updateTime,director:r.director,establishTime:r.establishTime||((o=e.data.createTime)==null?void 0:o.substring(0,7))||"",contactPhone:r.contactPhone,address:r.address,email:r.email,viewCount:e.data.pageViewCount||Math.floor(Math.random()*300)+100,publishDate:e.data.createTime||"2024-05-20",source:"原创",author:"管理员"},msg:e.msg}}return z(a)}).catch(e=>(console.error("获取分会详情失败:",e),z(a)))}function z(a){return{code:1,data:{id:a,name:a===1?"标准化工作委员会":a===2?"针刀医学分会":a===3?"逆袭糖尿病专业委员会":"专业委员会",isNew:a===1||a===12||a===6,description:"专注于相关领域的学术研究、技术推广与交流，致力于提高诊疗水平，推动学科发展。",content:`<p>河南省软组织病研究会${a===1?"标准化工作委员会":a===2?"针刀医学分会":a===3?"逆袭糖尿病专业委员会":"专业委员会"}是河南省软组织病研究会下设的专业委员会之一。委员会由我省相关领域的知名专家、学者组成，致力于开展学术研究、培训、交流及推广工作，提高诊疗水平，推动学科发展。</p>
      <h3>主要职责</h3>
      <ul>
        <li>组织开展学术交流、研讨及培训活动</li>
        <li>推广诊疗新技术、新方法</li>
        <li>编写临床诊疗指南与专家共识</li>
        <li>开展相关科研项目</li>
        <li>促进专业人才培养</li>
      </ul>
      <h3>委员会成员</h3>
      <p><strong>主任委员：</strong>张明 (主任医师，河南省人民医院)</p>
      <p><strong>副主任委员：</strong>李华 (主任医师，郑州大学第一附属医院)</p>
      <p><strong>副主任委员：</strong>王强 (主任医师，河南省中医院)</p>
      <p><strong>秘书长：</strong>刘伟 (副主任医师，郑州市第一人民医院)</p>`,createTime:"2023-05-28 02:50:01",updateTime:"2024-05-20 10:30:15",director:a===1?"张明":a===2?"李华":"王强",establishTime:a===1?"2023年05月":a===2?"2022年10月":"2021年08月",contactPhone:"0371-XXXXXXXX",address:"河南省郑州市金水区文化路97号河南省人民医院",email:"<EMAIL>",viewCount:Math.floor(Math.random()*300)+100,publishDate:"2024-05-20",source:"原创",author:"管理员"},msg:"操作成功(默认数据)"}}function R(){return U.get("/website/branch/getAll").then(a=>{if(console.log("从后台获取分会列表结果:",a),a&&a.code===0&&a.data){const e=a.data.map(o=>({id:o.branchId,name:o.branchName,isNew:!1,description:o.description||"",code:o.branchCode||"",logo:o.logo||"",sort:o.sort||0}));return{code:0,data:{list:e,total:e.length},msg:"操作成功"}}return X()}).catch(a=>(console.error("从后台获取分会列表失败:",a),X()))}const tt={getBranchList:X,getBackendBranchList:R,getBranchDetail:Z},et={class:"branch-container"},at={class:"container"},st={class:"branch-content"},nt={class:"branch-layout"},ot={class:"branch-sidebar"},it={class:"branch-list"},lt=["onClick"],rt={key:0,class:"branch-tag"},ct={class:"branch-articles"},dt={class:"articles-header"},ut={class:"articles-title"},ht={key:0,class:"articles-count"},mt={class:"min-height-container"},pt={key:0,class:"article-list"},gt=["onClick"],vt={class:"article-date"},ft={class:"article-content"},_t={class:"article-title"},bt={key:0,class:"article-summary"},yt={class:"article-meta"},Nt={class:"article-views"},wt={key:0,class:"article-branch"},Tt={key:1,class:"empty-articles"},Ct={key:0,class:"pagination-container"},kt={__name:"index",setup(a){const e=O(),o=p(!1),r=p(!1),t=p([]),u=p([]),d=p(null),f=p(""),g=p(1),_=p(10),i=p(0),y=async()=>{try{o.value=!0;const n=await tt.getBranchList();n&&n.data?t.value=n.data||[]:t.value=[],console.log("分会数据加载完成")}catch(n){console.error("加载分会数据失败:",n),$.error("加载分会数据失败"),t.value=[]}finally{o.value=!1}},w=async()=>{try{r.value=!0;const n={pageNum:g.value,pageSize:_.value,branchId:d.value,status:1,deletedFlag:!1},s=await K.getContentList(n);s&&s.data?(u.value=s.data.list||[],i.value=s.data.total||0):(u.value=[],i.value=0)}catch(n){console.error("获取文章列表失败:",n),$.error("获取文章列表失败"),u.value=[],i.value=0}finally{r.value=!1}},B=n=>{n?(d.value=n.id,f.value=n.name):(d.value=null,f.value=""),g.value=1,w()},x=n=>{g.value=n,w()},F=n=>{e.push({path:`/branch-article/${n.id}`,query:{title:n.title}})},P=n=>{if(!n)return"";const s=new Date(n);return`${s.getFullYear()}-${String(s.getMonth()+1).padStart(2,"0")}-${String(s.getDate()).padStart(2,"0")}`},A=n=>{if(!n)return"";const s=t.value.find(T=>T.id===n);return s?s.name:""};return G(()=>{console.log("分会页面已加载"),y(),w()}),(n,s)=>{const T=W("a-empty"),M=W("a-spin"),V=W("a-pagination");return m(),h("div",et,[l("div",at,[s[7]||(s[7]=l("h1",{class:"page-title"},"分会",-1)),l("div",st,[s[6]||(s[6]=l("p",{class:"page-description"}," 河南省软组织病研究会下设若干分会，充分调动各专业领域专家的积极性，促进软组织病学科建设和发展。以下是各分会的相关文章和动态。 ",-1)),l("div",nt,[l("div",ot,[s[4]||(s[4]=l("h3",{class:"sidebar-title"},"分会列表",-1)),l("ul",it,[l("li",{class:D(["branch-item all-branches",{active:d.value===null}]),onClick:s[0]||(s[0]=c=>B(null))},s[2]||(s[2]=[l("i",{class:"branch-icon"},"📋",-1),I(" 全部分会 ")]),2),(m(!0),h(L,null,S(t.value,(c,C)=>(m(),h("li",{key:C,class:D(["branch-item",{active:d.value===c.id}]),onClick:q=>B(c)},[s[3]||(s[3]=l("i",{class:"branch-icon"},"🏢",-1)),I(" "+v(c.name)+" ",1),c.isNew?(m(),h("span",rt,"新")):b("",!0)],10,lt))),128))])]),l("div",ct,[l("div",dt,[l("h3",ut,v(f.value||"全部分会")+"文章",1),i.value>0?(m(),h("div",ht,"共 "+v(i.value)+" 篇文章",1)):b("",!0)]),k(M,{spinning:r.value,tip:"加载中..."},{default:J(()=>[l("div",mt,[u.value.length>0?(m(),h("div",pt,[(m(!0),h(L,null,S(u.value,(c,C)=>(m(),h("div",{key:C,class:"article-item",onClick:q=>F(c)},[l("div",vt,v(P(c.publishTime)),1),l("div",ft,[l("div",_t,v(c.title),1),c.summary?(m(),h("div",bt,v(c.summary),1)):b("",!0),l("div",yt,[l("span",Nt,"浏览量: "+v(c.pageViewCount||0),1),c.branchId&&!d.value?(m(),h("span",wt," 所属分会: "+v(A(c.branchId)),1)):b("",!0),s[5]||(s[5]=l("span",{class:"read-more"},"阅读更多",-1))])])],8,gt))),128))])):(m(),h("div",Tt,[k(T,{description:d.value?`暂无${f.value}相关文章`:"暂无文章"},null,8,["description"])]))])]),_:1},8,["spinning"]),u.value.length>0?(m(),h("div",Ct,[k(V,{current:g.value,"onUpdate:current":s[1]||(s[1]=c=>g.value=c),total:i.value,pageSize:_.value,onChange:x,"show-quick-jumper":"","show-total":c=>`共 ${c} 项`},null,8,["current","total","pageSize","show-total"])])):b("",!0)])])])])])}}},Xt=Y(kt,[["__scopeId","data-v-17988b9f"]]);export{Xt as default};
