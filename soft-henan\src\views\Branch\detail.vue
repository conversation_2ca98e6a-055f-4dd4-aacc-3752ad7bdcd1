<template>
  <div class="branch-detail-container"><div class="container">
      <div class="breadcrumb">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/">首页</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <router-link to="/branch">分会</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{ branchDetail.name }}</a-breadcrumb-item>
        </a-breadcrumb>
      </div>

      <div class="detail-wrapper">
        <div class="action-bar">
          <a-button @click="goBack" type="primary">
            <template #icon><left-outlined /></template>
            返回列表
          </a-button>
        </div>

        <a-spin :spinning="loading" tip="加载中...">
          <div class="detail-content">
            <h1 class="detail-title">{{ branchDetail.name }}</h1>
            <div class="detail-meta">
              <span>发布时间：{{ formatDate(branchDetail.publishDate) }}</span>
              <span class="meta-divider">|</span>
              <span>来源：{{ branchDetail.source }}</span>
              <span class="meta-divider">|</span>
              <span>作者：{{ branchDetail.author }}</span>
              <span class="meta-divider">|</span>
              <span>浏览量：{{ branchDetail.viewCount || 0 }}</span>
              <span class="meta-divider" v-if="branchDetail.isNew">|</span>
              <span class="detail-tag" v-if="branchDetail.isNew">新成立</span>
            </div>

            <div class="info-section">
              <div class="info-table">
                <div class="info-row">
                  <div class="info-label">负责人：</div>
                  <div class="info-value">{{ branchDetail.director }}</div>
                </div>
                <div class="info-row">
                  <div class="info-label">成立时间：</div>
                  <div class="info-value">{{ branchDetail.establishTime }}</div>
                </div>
                <div class="info-row">
                  <div class="info-label">联系电话：</div>
                  <div class="info-value">{{ branchDetail.contactPhone }}</div>
                </div>
                <div class="info-row">
                  <div class="info-label">办公地址：</div>
                  <div class="info-value">{{ branchDetail.address }}</div>
                </div>
                <div class="info-row">
                  <div class="info-label">电子邮箱：</div>
                  <div class="info-value">{{ branchDetail.email }}</div>
                </div>
              </div>
            </div>

            <div class="content-section">
              <div class="content-text" v-html="branchDetail.content"></div>
            </div>
          </div>
        </a-spin>
      </div>
    </div></div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { LeftOutlined } from '@ant-design/icons-vue'
// 如果日期格式为 yyyy-MM-dd HH:mm:ss，则只取日期部分
  if (dateString.includes(' ')) {
    return dateString.split(' ')[0];
  }

// 返回列表
const goBack = () => {
  // 使用replace而不是push，这样可以替换当前历史记录，避免浏览器返回按钮的问题
  router.replace('/branch')

  // 添加一个小延迟，确保DOM更新后滚动条保持可见
  setTimeout(() => {
    document.documentElement.style.overflowY = 'scroll'
    document.body.style.overflowY = 'scroll'
  }, 100)
}

onMounted(() => {
  fetchBranchDetail()
})
</script>

<style scoped>
.branch-detail-container {
  overflow-x: hidden; /* 防止水平滚动条 */
}

.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0 40px;
  overflow-x: hidden; /* 防止水平滚动条 */
}

.breadcrumb {
  margin-bottom: 20px;
}

.detail-wrapper {
  min-height: 500px;
  position: relative;
  overflow: hidden; /* 防止内容溢出 */
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.detail-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box; /* 确保padding不会增加宽度 */
  min-height: 400px; /* 确保最小高度 */
}

.detail-title {
  font-size: 28px;
  color: #333;
  text-align: center;
  margin-bottom: 20px;
}

.detail-meta {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px dashed #eee;
  display: flex;
  justify-content: center;
  align-items: center;
}

.meta-divider {
  margin: 0 8px;
  color: #ddd;
}

.detail-tag {
  background-color: var(--primary-color);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.info-section {
  margin-bottom: 30px;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 15px 20px 15px 0;
  position: relative;
}

.info-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--primary-color);
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.info-table {
  width: 100%;
  padding-left: 15px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  line-height: 1.6;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 100px;
  font-weight: 500;
  color: #333;
  text-align: right;
}

.info-value {
  flex: 1;
  color: #666;
  padding-left: 10px;
}

.content-section {
  line-height: 1.8;
  color: #333;
  margin-bottom: 30px;
}

.content-text {
  line-height: 1.8;
  color: #333;
}

.content-text p {
  margin-bottom: 15px;
}

.detail-body :deep(h3) {
  font-size: 18px;
  color: var(--primary-color);
  margin: 25px 0 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #eee;
}

.detail-body :deep(p) {
  margin-bottom: 15px;
}

.detail-body :deep(ul) {
  padding-left: 20px;
  margin-bottom: 20px;
}

.detail-body :deep(li) {
  margin-bottom: 10px;
}
</style>
