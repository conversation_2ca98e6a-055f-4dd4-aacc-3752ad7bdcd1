import{_ as N,r as p,o as W,w as O,c as a,a as t,b as _,d as f,e as R,f as x,g as A,h as s,i as C,t as d,F as w,j as k,k as V,l as b}from"./index-DIypJWEE.js";const $={class:"about-container"},F={class:"container"},j={class:"about-content"},U={class:"min-height-container"},J={class:"section"},q={class:"section-content"};const G=["innerHTML"],Q={key:2},X={key:3},Y={class:"section"};const ee=["innerHTML"],te={key:2},oe={key:3},ne={class:"section-content"},ae={class:"leader-list"},se={class:"vice-presidents"},le={class:"leader-image"},ie=["src","alt"],re={class:"leader-info"},ce={class:"leader-name"},de={class:"leader-desc"},ue={class:"section-content"},pe={class:"secretary-list"},ve={class:"section"},me={class:"section-content statute"};const ge=["innerHTML"],ye={key:2},he={key:3},_e={class:"section"},fe={class:"section-content contact"};const we=["innerHTML"],ke={key:2},Me={key:3},Ae={class:"map-section"},Ce={id:"map",class:"map-content",ref:"mapContainer"},be={__name:"index",setup(xe){const h=p(!1),M=p("1"),v=p(null),m=p(null);p(null);const g=p(null),u=p(null),l=p({title:"河南省软组织病研究会",address:"",longitude:113.665412,latitude:34.757975,zoom:15}),z=async()=>{try{console.log("开始获取学会简介内容");const e=await A.getAssociationIntro();console.log("学会简介内容获取结果:",e),e&&e.data&&e.data.list&&e.data.list.length>0?(console.log("设置学会简介内容:",e.data.list[0]),v.value=e.data.list[0]):console.warn("未获取到学会简介内容数据")}catch(e){console.error("获取学会简介内容失败:",e)}},H=async()=>{try{console.log("开始获取组织机构内容");const e=await A.getAssociationOrganization();console.log("组织机构内容获取结果:",e),e&&e.data&&e.data.list&&e.data.list.length>0?(console.log("设置组织机构内容:",e.data.list[0]),m.value=e.data.list[0]):console.warn("未获取到组织机构内容数据")}catch(e){console.error("获取组织机构内容失败:",e)}},E=async()=>{try{console.log("开始获取章程制度内容");const e=await A.getAssociationStatute();console.log("章程制度内容获取结果:",e),e&&e.data&&e.data.list&&e.data.list.length>0?(console.log("设置章程制度内容:",e.data.list[0]),g.value=e.data.list[0]):console.warn("未获取到章程制度内容数据")}catch(e){console.error("获取章程制度内容失败:",e)}},L=async()=>{try{console.log("开始获取联系我们内容");const e=await A.getAssociationContact();if(console.log("联系我们内容获取结果:",e),e&&e.data&&e.data.list&&e.data.list.length>0){if(console.log("设置联系我们内容:",e.data.list[0]),u.value=e.data.list[0],u.value.contentHtml){const o=u.value.contentHtml.match(/<strong>地址：<\/strong>([^<]+)<\/p>/i);o&&o[1]&&(l.value.address=o[1].trim())}}else console.warn("未获取到联系我们内容数据")}catch(e){console.error("获取联系我们内容失败:",e)}},S=async()=>{try{console.log("开始获取网站基本配置");const e=await configApi.getWebsiteBasicConfig();console.log("网站基本配置获取结果:",e),e&&e.data&&(e.data.mapCenterLongitude&&e.data.mapCenterLatitude&&(l.value.center=[e.data.mapCenterLongitude,e.data.mapCenterLatitude]),e.data.mapZoom&&(l.value.zoom=e.data.mapZoom),e.data.mapMarkerTitle&&(l.value.title=e.data.mapMarkerTitle),e.data.mapMarkerAddress&&(l.value.address=e.data.mapMarkerAddress),e.data.mapApiKey&&(l.value.apiKey=e.data.mapApiKey),e.data.mapSecurityCode&&(l.value.securityCode=e.data.mapSecurityCode),console.log("更新后的地图配置:",l.value))}catch(e){console.error("获取网站基本配置失败:",e)}},P=async()=>{h.value=!0;try{await Promise.all([S(),z(),H(),E(),L()])}catch(e){console.error("初始化数据失败:",e),R.error("获取数据失败，请稍后再试")}finally{h.value=!1}},Z=()=>new Promise((e,o)=>{if(window.AMap){e(window.AMap);return}if(!l.value.apiKey){o(new Error("未配置高德地图API密钥"));return}l.value.securityCode&&(window._AMapSecurityConfig={securityJsCode:l.value.securityCode});const n=document.getElementById("amap-script");n&&document.head.removeChild(n);const c=document.createElement("script");c.id="amap-script",c.type="text/javascript",c.src=`https://webapi.amap.com/maps?v=2.0&key=${l.value.apiKey}&plugin=AMap.Scale,AMap.ToolBar,AMap.ControlBar,AMap.DistrictSearch,AMap.Buildings,AMap.IndoorMap&showBuildingBlock=true&viewMode=3D`,c.async=!0,console.log("开始加载高德地图API，URL:",c.src),c.onerror=y=>{console.error("加载高德地图API失败:",y),o(new Error("加载高德地图API失败"))},c.onload=()=>{window.AMap?(console.log("高德地图API加载成功"),e(window.AMap)):(console.error("加载高德地图API失败: AMap对象不存在"),o(new Error("加载高德地图API失败: AMap对象不存在")))},document.head.appendChild(c)}),D=async()=>{const e=document.getElementById("map");if(!e){console.error("地图容器不存在");return}try{await Z(),console.log("开始初始化地图，配置:",l.value),console.log("创建地图实例，配置缩放级别:",l.value.zoom);const o=parseInt(l.value.zoom)||15;console.log("解析后的缩放级别:",o);const n=new AMap.Map("map",{zoom:o,center:l.value.center,resizeEnable:!0,viewMode:"3D",pitch:30,rotation:0,jogEnable:!0,animateEnable:!0,dragEnable:!0,zoomEnable:!0,doubleClickZoom:!0,mapStyle:"amap://styles/normal",showBuildingBlock:!0,showLabel:!0,buildingAnimation:!0,expandZoomRange:!0,zooms:[3,20]}),c=new AMap.Marker({position:l.value.center,title:l.value.title,animation:"AMAP_ANIMATION_DROP",clickable:!0,topWhenClick:!0,zIndex:110}),y=new AMap.Buildings({zooms:[15,20],zIndex:10,heightFactor:2});n.add(y),n.add(c);const i=new AMap.InfoWindow({content:`<div style="padding: 15px; max-width: 280px; background-color: white; border-radius: 4px;">
        <h4 style="margin: 0 0 8px 0; font-size: 16px; color: var(--primary-color); border-bottom: 1px solid #eee; padding-bottom: 8px;">${l.value.title}</h4>
        <p style="margin: 0; font-size: 14px; color: #333; line-height: 1.5;">
          ${l.value.address}
        </p>
      </div>`,offset:new AMap.Pixel(0,-30),closeWhenClickMap:!0});c.on("click",()=>{i.open(n,c.getPosition())}),i.open(n,c.getPosition()),n.plugin(["AMap.ToolBar"],()=>{n.addControl(new AMap.ToolBar({position:"RB",offset:[10,10],ruler:!0,direction:!0,locate:!0}))}),n.plugin(["AMap.Scale"],()=>{n.addControl(new AMap.Scale)}),n.plugin(["AMap.ControlBar"],()=>{n.addControl(new AMap.ControlBar({position:{top:"10px",right:"10px"},showZoomBar:!0,showControlButton:!0}))}),n.on("complete",()=>{console.log("地图加载完成，当前缩放级别:",n.getZoom()),setTimeout(()=>{const r=o,T=r>10?r-2:r+2;console.log("临时设置缩放级别:",T),n.setZoom(T),setTimeout(()=>{console.log("设置目标缩放级别:",r),n.setZoom(r),n.setPitch(30),console.log("调整视图以适应标记点"),n.setFitView([c],!1,[80,80,80,80]),setTimeout(()=>{const I=n.getZoom();console.log("最终缩放级别:",I),Math.abs(I-r)>.5&&(console.log("缩放级别不正确，强制设置为:",r),n.setZoom(r)),y&&y.setOptions({visible:!0,opacity:1})},300)},300)},300)}),console.log("地图初始化成功")}catch(o){console.error("初始化地图失败:",o),e.innerHTML=`
      <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; background-color: #f5f5f5;">
        <div style="font-size: 16px; color: #666; margin-bottom: 10px;">地图加载失败</div>
        <div style="font-size: 14px; color: #999;">${o.message||"请确保网络连接正常"}</div>
        <div style="margin-top: 20px; font-size: 14px; color: #666;">
          <strong>地址：</strong>${l.value.address}
        </div>
      </div>
    `}},B=async e=>{h.value=!0,e==="4"?(console.log("切换到联系我们标签页，准备初始化地图"),setTimeout(async()=>{try{console.log("开始初始化地图"),await D(),console.log("地图初始化完成"),window.addEventListener("resize",()=>{const o=document.getElementById("map");o&&o.__amap__&&(console.log("窗口大小变化，调整地图大小"),o.__amap__.resize())})}catch(o){console.error("初始化地图失败:",o)}finally{h.value=!1}},300)):setTimeout(()=>{h.value=!1},100)},K=e=>{B(e)};return W(()=>{P(),M.value="1",O(M,K)}),(e,o)=>{const n=x("a-tab-pane"),c=x("a-tabs"),y=x("a-spin");return s(),a("div",$,[t("div",F,[o[17]||(o[17]=t("h1",{class:"page-title"},"关于学会",-1)),t("div",j,[_(y,{spinning:h.value,tip:"加载中..."},{default:f(()=>[t("div",U,[_(c,{activeKey:M.value,"onUpdate:activeKey":o[0]||(o[0]=i=>M.value=i),onChange:B},{default:f(()=>[_(n,{key:"1",tab:"学会简介"},{default:f(()=>[t("div",J,[o[2]||(o[2]=t("div",{class:"section-header"},[t("h2",null,"河南省软组织病研究会简介")],-1)),t("div",q,[C("",!0),v.value&&v.value.contentHtml?(s(),a("div",{key:1,innerHTML:v.value.contentHtml},null,8,G)):v.value&&v.value.contentText?(s(),a("div",Q,[(s(!0),a(w,null,k(v.value.contentText.split(`

`),(i,r)=>(s(),a("p",{key:r},d(i),1))),128))])):(s(),a("div",X,o[1]||(o[1]=[t("p",null,"河南省软组织病研究会(Henan Association for Soft Tissue Disease Research)是由河南省卫生健康委员会业务主管，河南省软组织病领域专家、科技工作者与热心支持软组织病研究事业的单位和个人自愿结成， 依法由河南省民政厅批准登记成立，服务于软组织病行业和地方的性、学术性、非营利性社会组织。",-1),t("p",null,"学会以党的十九大精神和习近平新时代中国特色社会主义思想为指导，遵守宪法、法律、法规和国家政策，践行社会主义核心价值观， 遵守社会道德风尚，不损害国家利益、社会公共利益以及其他组织和公民的合法权益。贯彻党和政府有关发展软组织病研究工作的方针政策， 坚持医学研究创新，促进软组织病研究和技术交流，促进软组织病防治人才的成长和提高，为提高河南省人民的健康水平和促进有中国特色的医药卫生服务。",-1),t("p",null,"主要业务范围是学术交流、理论研究、产业促进、技术推广、咨询服务。",-1)])))])])]),_:1}),_(n,{key:"2",tab:"组织机构"},{default:f(()=>[t("div",Y,[o[7]||(o[7]=t("div",{class:"section-header"},[t("h2",null,"组织机构")],-1)),C("",!0),m.value&&m.value.contentHtml?(s(),a("div",{key:1,innerHTML:m.value.contentHtml},null,8,ee)):m.value&&m.value.contentText?(s(),a("div",te,[(s(!0),a(w,null,k(m.value.contentText.split(`

`),(i,r)=>(s(),a("p",{key:r},d(i),1))),128))])):(s(),a("div",oe,[o[5]||(o[5]=t("div",{class:"section-header"},[t("h2",null,"理事会")],-1)),t("div",ne,[t("div",ae,[o[4]||(o[4]=t("div",{class:"leader-item president"},[t("div",{class:"leader-image"},[t("img",{src:V,alt:"会长"})]),t("div",{class:"leader-info"},[t("h3",{class:"leader-name"},"王海亮"),t("p",{class:"leader-title"},"会长"),t("p",{class:"leader-desc"},"河南省人民医院主任医师，教授，博士生导师")])],-1)),t("div",se,[(s(!0),a(w,null,k(e.vicePresidents,(i,r)=>(s(),a("div",{class:"leader-item",key:r},[t("div",le,[t("img",{src:i.image,alt:i.name},null,8,ie)]),t("div",re,[t("h3",ce,d(i.name),1),o[3]||(o[3]=t("p",{class:"leader-title"},"副会长",-1)),t("p",de,d(i.desc),1)])]))),128))])])]),o[6]||(o[6]=t("div",{class:"section-header"},[t("h2",null,"秘书处")],-1)),t("div",ue,[t("div",pe,[(s(!0),a(w,null,k(e.secretaryMembers,(i,r)=>(s(),a("div",{class:"secretary-item",key:r},[t("h3",null,d(i.position),1),t("p",null,d(i.name)+" "+d(i.title),1)]))),128))])])]))])]),_:1}),_(n,{key:"3",tab:"章程制度"},{default:f(()=>[t("div",ve,[o[9]||(o[9]=t("div",{class:"section-header"},[t("h2",null,"河南省软组织病研究会章程")],-1)),t("div",me,[C("",!0),g.value&&g.value.contentHtml?(s(),a("div",{key:1,innerHTML:g.value.contentHtml},null,8,ge)):g.value&&g.value.contentText?(s(),a("div",ye,[(s(!0),a(w,null,k(g.value.contentText.split(`

`),(i,r)=>(s(),a("p",{key:r},d(i),1))),128))])):(s(),a("div",he,o[8]||(o[8]=[t("h3",null,"第一章 总则",-1),t("p",null,"第一条 本团体的名称：河南省软组织病研究会。",-1),t("p",null,"第二条 本团体的性质：由河南省软组织病领域专家、科技工作者与热心支持软组织病研究事业的单位和个人自愿结成的全省性、学术性、非营利性社会组织。",-1),t("p",null,"第三条 本团体的宗旨：以党的十九大精神和习近平新时代中国特色社会主义思想为指导，本会遵守宪法、法律、法规和国家政策，践行社会主义核心价值观，遵守社会道德风尚，不损害国家利益、社会公共利益以及其他组织和公民的合法权益。贯彻党和政府有关发展软组织病研究工作的方针政策，坚持医学研究创新，促进软组织病研究和技术交流，促进软组织病防治人才的成长和提高，为提高河南省人民的健康水平和促进有中国特色的医药卫生服务。",-1),t("p",null,"第四条 本团体的业务主管单位：河南省卫生健康委员会，登记管理机关：河南省民政厅。",-1),t("p",null,"第五条 本团体的住所：河南省郑州市金水区。",-1),t("h3",null,"第二章 业务范围",-1),t("p",null,"第六条 本团体的业务范围：",-1),t("p",null,"（一）开展软组织病防治学术交流、科研协作并推广其新技术、新成果。",-1),t("p",null,"（二）编辑出版相关学术资料和科技信息。",-1),t("p",null,"（三）组织开展软组织病防治科学知识的普及教育。",-1),t("p",null,"（四）组织开展软组织病防治人才培训。",-1),t("p",null,"（五）开展软组织病防治科研项目评审和成果评价。",-1),t("p",null,"（六）推荐和举荐软组织病防治人才。",-1),t("p",null,"（七）对软组织病防治工作中的重大课题和发展规划提出建议。",-1),t("p",null,"（八）开展志愿服务活动，弘扬社会主义核心价值观。",-1)])))])])]),_:1}),_(n,{key:"4",tab:"联系我们"},{default:f(()=>[t("div",_e,[o[16]||(o[16]=t("div",{class:"section-header"},[t("h2",null,"联系我们")],-1)),t("div",fe,[C("",!0),u.value&&u.value.contentHtml?(s(),a("div",{key:1,innerHTML:u.value.contentHtml},null,8,we)):u.value&&u.value.contentText?(s(),a("div",ke,[(s(!0),a(w,null,k(u.value.contentText.split(`

`),(i,r)=>(s(),a("p",{key:r},d(i),1))),128))])):(s(),a("div",Me,[o[14]||(o[14]=t("h3",null,"联系方式",-1)),t("p",null,[o[10]||(o[10]=t("strong",null,"地址：",-1)),b(d(e.contactInfo.address),1)]),t("p",null,[o[11]||(o[11]=t("strong",null,"电话：",-1)),b(d(e.contactInfo.phone),1)]),t("p",null,[o[12]||(o[12]=t("strong",null,"邮箱：",-1)),b(d(e.contactInfo.email),1)]),t("p",null,[o[13]||(o[13]=t("strong",null,"官方微信：",-1)),b(d(e.contactInfo.wechat.text),1)])]))]),t("div",Ae,[o[15]||(o[15]=t("div",{class:"section-header"},[t("h2",null,"位置地图")],-1)),t("div",Ce,null,512)])])]),_:1})]),_:1},8,["activeKey"])])]),_:1},8,["spinning"])])])])}}},Te=N(be,[["__scopeId","data-v-890e8791"]]);export{Te as default};
