import{_ as T,r as y,o as w,c as i,a,b as r,d,u as x,e as m,p as z,f as u,s as B,h as n,i as l,t as o,F as L,j as V,l as h}from"./index-DIypJWEE.js";const F={class:"activity-detail-container"},I={class:"container"},M={class:"detail-wrapper"},R={key:0,class:"activity-detail"},$={class:"activity-title"},j={class:"activity-meta"},A={class:"meta-item"},E={class:"meta-value"},S={class:"meta-item"},U={class:"meta-value"},W={key:0,class:"meta-item"},q={class:"meta-value"},G={key:1,class:"meta-item"},J={class:"meta-value"},K={key:0,class:"activity-summary"},O={class:"summary-content"},Q=["innerHTML"],X={key:1,class:"activity-attachments"},Y={class:"attachments-list"},Z=["href"],P={class:"activity-actions"},tt={key:1,class:"empty-data"},at={class:"empty-actions"},et={__name:"detail",setup(st){const f=x(),g=B(),c=y(!1),e=y({}),k=async()=>{try{c.value=!0;const s=f.params.id;if(!s){m.error("缺少活动ID参数");return}const t=await z(s);t&&(t.code===0||t.code===1||t.code===200)&&t.data?e.value={id:t.data.contentId,title:t.data.title,time:b(t.data.publishTime||t.data.createTime),location:t.data.summary||"待定",organizer:"河南省软组织病研究会",contact:"0371-12345678",summary:t.data.summary,contentHtml:t.data.contentHtml||t.data.description,attachments:t.data.attachments||[]}:m.error("获取活动详情失败")}catch(s){console.error("获取活动详情失败:",s),m.error("获取活动详情失败: "+(s.message||"未知错误"))}finally{c.value=!1}},b=s=>s?new Date(s).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"",D=s=>s?s.startsWith("http")?s:`http://127.0.0.1:8080/files/${s}`:"",_=()=>{g.go(-1)};return w(()=>{k()}),(s,t)=>{const v=u("a-button"),C=u("a-empty"),H=u("a-spin");return n(),i("div",F,[a("div",I,[a("div",M,[r(H,{spinning:c.value,tip:"加载中..."},{default:d(()=>[e.value.id?(n(),i("div",R,[a("h1",$,o(e.value.title),1),a("div",j,[a("div",A,[t[0]||(t[0]=a("span",{class:"meta-label"},"活动时间：",-1)),a("span",E,o(e.value.time),1)]),a("div",S,[t[1]||(t[1]=a("span",{class:"meta-label"},"活动地点：",-1)),a("span",U,o(e.value.location),1)]),e.value.organizer?(n(),i("div",W,[t[2]||(t[2]=a("span",{class:"meta-label"},"主办单位：",-1)),a("span",q,o(e.value.organizer),1)])):l("",!0),e.value.contact?(n(),i("div",G,[t[3]||(t[3]=a("span",{class:"meta-label"},"联系方式：",-1)),a("span",J,o(e.value.contact),1)])):l("",!0)]),e.value.summary?(n(),i("div",K,[t[4]||(t[4]=a("div",{class:"summary-title"},"活动简介：",-1)),a("div",O,o(e.value.summary),1)])):l("",!0),a("div",{class:"activity-content",innerHTML:e.value.contentHtml},null,8,Q),e.value.attachments&&e.value.attachments.length>0?(n(),i("div",X,[t[5]||(t[5]=a("div",{class:"attachments-title"},"附件：",-1)),a("div",Y,[(n(!0),i(L,null,V(e.value.attachments,(N,p)=>(n(),i("div",{key:p,class:"attachment-item"},[a("a",{href:D(N),target:"_blank"},"附件"+o(p+1),9,Z)]))),128))])])):l("",!0),a("div",P,[r(v,{onClick:_},{default:d(()=>t[6]||(t[6]=[h("返回列表")])),_:1})])])):c.value?l("",!0):(n(),i("div",tt,[r(C,{description:"未找到活动信息"}),a("div",at,[r(v,{onClick:_},{default:d(()=>t[7]||(t[7]=[h("返回列表")])),_:1})])]))]),_:1},8,["spinning"])])])])}}},nt=T(et,[["__scopeId","data-v-851a895b"]]);export{nt as default};
