import{_ as h,r as u,o as T,m as k,q as I,e as w,c as l,a as n,b as _,d as y,f as p,h as d,F as x,j as C,i as D,t as v,l as N}from"./index-DIypJWEE.js";const b={class:"activity-container"},V={class:"container"},j={class:"activity-content"},B={class:"min-height-container"},F={key:0,class:"activity-list"},q={class:"activity-image"},z=["src","alt"],A={class:"activity-info"},L={class:"activity-date"},R={class:"activity-title"},S={class:"activity-desc"},E={key:1,class:"empty-data"},M={__name:"index",setup($){const r=u(!1),c=u([]);T(()=>{console.log("活动页面已加载"),g()});const g=async()=>{try{r.value=!0;const t=await k();if(console.log("获取内容类型结果:",t),t&&(t.code===0||t.code===1||t.code===200)&&t.data){const s=t.data.find(i=>i.contentTypeCode==="activity");if(console.log("找到的活动类型:",s),s){const i={contentTypeId:s.contentTypeId,pageNum:1,pageSize:10,status:1,deletedFlag:!1};console.log("查询活动参数:",i);const e=await I(i);console.log("获取活动结果:",e),e&&(e.code===0||e.code===1||e.code===200)&&e.data&&e.data.list?(c.value=e.data.list.map(o=>({title:o.title,publishTime:o.publishTime||o.createTime,description:o.description||o.summary||"暂无描述",image:o.coverImage||"/images/default-activity.jpg",contentId:o.contentId})),console.log("获取到的活动数据:",c.value)):(console.warn("获取活动数据失败或无数据:",e),c.value=[])}else console.warn("未找到activity内容类型，请检查后台内容类型配置"),c.value=[]}else console.warn("获取内容类型失败或内容类型数据为空:",t),c.value=[]}catch(t){console.error("获取活动数据失败:",t),w.error("获取活动数据失败"),c.value=[]}finally{r.value=!1}},m=t=>t?new Date(t).toLocaleDateString("zh-CN"):"";return(t,s)=>{const i=p("router-link"),e=p("a-empty"),o=p("a-spin");return d(),l("div",b,[n("div",V,[s[1]||(s[1]=n("h1",{class:"page-title"},"活动信息",-1)),n("div",j,[_(o,{spinning:r.value,tip:"加载中..."},{default:y(()=>[n("div",B,[c.value.length>0?(d(),l("div",F,[(d(!0),l(x,null,C(c.value,(a,f)=>(d(),l("div",{class:"activity-item",key:a.contentId||f},[n("div",q,[a.image&&a.image!=="/images/default-activity.jpg"?(d(),l("img",{key:0,src:a.image,alt:a.title},null,8,z)):D("",!0)]),n("div",A,[n("div",L,v(m(a.publishTime||a.createTime)),1),n("h3",R,v(a.title),1),n("p",S,v(a.description||a.summary||"暂无描述"),1),_(i,{to:`/activity/detail/${a.contentId}`,class:"read-more"},{default:y(()=>s[0]||(s[0]=[N("阅读更多")])),_:2},1032,["to"])])]))),128))])):(d(),l("div",E,[_(e,{description:"暂无活动信息"})]))])]),_:1},8,["spinning"])])])])}}},H=h(M,[["__scopeId","data-v-d20cfa2c"]]);export{H as default};
