import{_ as U,r as a,o as B,c as _,a as s,b as o,d as i,f as c,h as r,i as m,t as l,n as R,l as n,e as y}from"./index-DIypJWEE.js";const S={class:"certificate-container"},M={class:"container"},q={class:"certificate-content"},E={class:"certificate-main"},K={class:"search-area"},T={class:"search-box"},$={key:0,class:"results-section"},j={class:"results-header"},A={class:"results-count"},F={class:"results-table"},G={key:1,class:"no-results"},H={class:"cert-detail"},J={class:"cert-row"},L={class:"cert-item"},O={class:"item-value"},P={class:"cert-item"},Q={class:"item-value"},W={class:"cert-row"},Y={class:"cert-item"},Z={class:"item-value"},ss={class:"cert-item"},ts={class:"item-value"},es={class:"cert-row"},ls={class:"cert-item"},as={class:"item-value"},is={class:"cert-item"},os={class:"item-value"},ns={class:"cert-row"},ds={class:"cert-item"},cs={class:"item-value"},rs={class:"cert-item"},us={class:"item-value"},vs={key:0,class:"cert-row"},ps={class:"cert-item full-width"},_s={class:"item-value"},ms={__name:"index",setup(fs){const u=a(!1),b=a(""),w=a(!1),g=a(!1),f=a([]),v=a(!1),e=a({}),x=[{title:"证书编号",dataIndex:"id",key:"id",width:150},{title:"证书类型",dataIndex:"type",key:"type",width:120},{title:"持证人",dataIndex:"name",key:"name",width:100},{title:"工作单位",dataIndex:"organization",key:"organization",width:200},{title:"发证日期",dataIndex:"issueDate",key:"issueDate",width:120},{title:"有效期至",dataIndex:"validUntil",key:"validUntil",width:120},{title:"操作",key:"action",width:100}];a([]);const C=async p=>{if(!p.trim()){y.warning("请输入查询条件");return}u.value=!0;try{f.value=[],w.value=!1,g.value=!0,u.value=!1,y.info("暂无匹配的证书记录")}catch(t){console.error("查询证书失败:",t),u.value=!1,y.error("查询失败，请稍后重试")}},X=p=>{e.value=p,v.value=!0},k=()=>{v.value=!1,e.value={}};return B(()=>{console.log("证书查询页面已加载")}),(p,t)=>{const I=c("a-input-search"),h=c("a-button"),V=c("a-table"),z=c("a-spin"),D=c("a-modal");return r(),_("div",S,[s("div",M,[t[8]||(t[8]=s("h1",{class:"page-title"},"证书查询",-1)),s("div",q,[o(z,{spinning:u.value,tip:"加载中..."},{default:i(()=>[s("div",E,[s("div",K,[t[2]||(t[2]=s("div",{class:"search-title"},"请输入证书编号或持证人姓名进行查询",-1)),s("div",T,[o(I,{value:b.value,"onUpdate:value":t[0]||(t[0]=d=>b.value=d),placeholder:"请输入证书编号或持证人姓名","enter-button":"查询",size:"large",onSearch:C,class:"search-input"},null,8,["value"])])]),w.value?(r(),_("div",$,[s("div",j,[t[3]||(t[3]=s("h3",null,"查询结果",-1)),s("span",A,"共找到 "+l(f.value.length)+" 条记录",1)]),s("div",F,[o(V,{columns:x,"data-source":f.value,pagination:!1,rowKey:"id",class:"certificate-table"},{bodyCell:i(({column:d,record:N})=>[d.key==="action"?(r(),R(h,{key:0,type:"primary",size:"small",onClick:hs=>X(N)},{default:i(()=>t[4]||(t[4]=[n(" 查看详情 ")])),_:2},1032,["onClick"])):m("",!0)]),_:1},8,["data-source"])])])):m("",!0),g.value?(r(),_("div",G,t[5]||(t[5]=[s("div",{class:"no-results-content"},[s("div",{class:"no-results-icon"},"🔍"),s("h3",null,"未找到相关证书"),s("p",null,"请检查输入的证书编号或持证人姓名是否正确")],-1)]))):m("",!0),t[6]||(t[6]=s("div",{class:"divider"},null,-1)),t[7]||(t[7]=s("div",{class:"certificate-info"},[s("h3",{class:"info-title"},"证书说明"),s("div",{class:"info-section"},[s("h4",{class:"section-title"},"证书类型"),s("ul",{class:"cert-type-list"},[s("li",null,[s("strong",null,"培训结业证书："),n("由河南省软组织病研究会组织的各类培训活动颁发的结业证书，证明持证人完成了相关培训课程。")]),s("li",null,[s("strong",null,"技能等级证书："),n("评定软组织病相关技能水平的证书，分为初级、中级、高级三个等级。")]),s("li",null,[s("strong",null,"专家资质证书："),n("认定软组织病领域专家资质的证书，由河南省软组织病研究会颁发。")])])]),s("div",{class:"info-section"},[s("h4",{class:"section-title"},"证书真伪查询"),s("p",{class:"query-desc"},"您可以通过本页面查询证书的真伪，核验证书信息是否与我会备案一致。如有疑问，请致电：0371-XXXXXXXX。")])],-1))])]),_:1},8,["spinning"])])]),o(D,{visible:v.value,"onUpdate:visible":t[1]||(t[1]=d=>v.value=d),title:"证书详情",width:"700px",onCancel:k},{footer:i(()=>[o(h,{onClick:k},{default:i(()=>t[19]||(t[19]=[n("关闭")])),_:1}),o(h,{type:"primary",danger:""},{default:i(()=>t[20]||(t[20]=[n("下载证书")])),_:1})]),default:i(()=>[s("div",H,[s("div",J,[s("div",L,[t[9]||(t[9]=s("span",{class:"item-label"},"证书编号：",-1)),s("span",O,l(e.value.id),1)]),s("div",P,[t[10]||(t[10]=s("span",{class:"item-label"},"证书类型：",-1)),s("span",Q,l(e.value.type),1)])]),s("div",W,[s("div",Y,[t[11]||(t[11]=s("span",{class:"item-label"},"持证人：",-1)),s("span",Z,l(e.value.name),1)]),s("div",ss,[t[12]||(t[12]=s("span",{class:"item-label"},"性别：",-1)),s("span",ts,l(e.value.gender),1)])]),s("div",es,[s("div",ls,[t[13]||(t[13]=s("span",{class:"item-label"},"工作单位：",-1)),s("span",as,l(e.value.organization),1)]),s("div",is,[t[14]||(t[14]=s("span",{class:"item-label"},"职称：",-1)),s("span",os,l(e.value.title),1)])]),s("div",ns,[s("div",ds,[t[15]||(t[15]=s("span",{class:"item-label"},"发证日期：",-1)),s("span",cs,l(e.value.issueDate),1)]),s("div",rs,[t[16]||(t[16]=s("span",{class:"item-label"},"有效期至：",-1)),s("span",us,l(e.value.validUntil),1)])]),e.value.description?(r(),_("div",vs,[s("div",ps,[t[17]||(t[17]=s("span",{class:"item-label"},"证书说明：",-1)),s("span",_s,l(e.value.description),1)])])):m("",!0),t[18]||(t[18]=s("div",{class:"cert-footer"},[s("div",{class:"cert-stamp"},[s("div",{class:"stamp-placeholder"},"公章")]),s("div",{class:"cert-issuer"}," 河南省软组织病研究会 ")],-1))])]),_:1},8,["visible"])])}}},bs=U(ms,[["__scopeId","data-v-5b05e391"]]);export{bs as default};
