<template>
  <div class="service-detail-container">
    <div class="container">
      <div class="detail-header">
        <a-button @click="goBack" class="back-btn">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
          返回
        </a-button>
        <h1 class="detail-title">{{ serviceDetail.title }}</h1>
      </div>

      <div class="detail-content">
        <a-spin :spinning="loading" tip="加载中...">
          <div v-if="!loading && serviceDetail.contentId">
            <!-- 服务信息 -->
            <div class="service-info">
              <div class="info-meta">
                <a-space>
                  <span>分类：{{ serviceDetail.contentTypeName || '服务' }}</span>
                  <a-divider type="vertical" />
                  <span>作者：{{ serviceDetail.author || '管理员' }}</span>
                  <a-divider type="vertical" />
                  <span>来源：{{ serviceDetail.source || '本会' }}</span>
                  <a-divider type="vertical" />
                  <span>发布时间：{{ formatDate(serviceDetail.publishTime) }}</span>
                  <a-divider type="vertical" />
                  <span>浏览量：{{ serviceDetail.pageViewCount || 0 }}</span>
                </a-space>
              </div>

              <!-- 服务标签 -->
              <div class="service-tags">
                <a-space>
                  <a-tag v-if="serviceDetail.topFlag" color="success">置顶</a-tag>
                  <a-tag v-if="serviceDetail.recommendFlag" color="success">推荐</a-tag>
                  <a-tag v-if="serviceDetail.status === 0" color="warning">草稿</a-tag>
                  <a-tag v-if="serviceDetail.status === 1" color="success">已发布</a-tag>
                  <a-tag v-if="serviceDetail.status === 2" color="error">已下线</a-tag>
                </a-space>
              </div>

              <!-- 服务摘要 -->
              <div class="service-summary" v-if="serviceDetail.summary">
                <div class="summary-title">摘要：</div>
                <div class="summary-content">{{ serviceDetail.summary }}</div>
              </div>

              <!-- 服务内容 -->
              <div class="service-content" v-html="serviceDetail.contentHtml"></div>

              <!-- 附件 -->
              <div class="service-attachments" v-if="serviceDetail.attachment && serviceDetail.attachment.length > 0">
                <div class="attachments-title">附件：</div>
                <div class="attachments-list">
                  <div v-for="(item, index) in serviceDetail.attachment" :key="index" class="attachment-item">
                    <a :href="item.fileUrl" target="_blank" class="attachment-link">
                      <FileTextOutlined />
                      {{ item.fileName || `附件${index + 1}` }}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空数据状态 -->
          <div v-else-if="!loading && !serviceDetail.contentId" class="empty-data">
            <a-empty description="服务内容不存在或已被删除" />
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { ArrowLeftOutlined, FileTextOutlined } from '@ant-design/icons-vue'
import { getContentDetail } from '../../api/content'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const serviceDetail = ref({})

// 页面加载
onMounted(() => {
  const contentId = route.query.id || route.params.id
  if (contentId) {
    queryServiceDetail(contentId)
  } else {
    message.error('缺少服务内容ID')
  }
})

// 查询服务详情
const queryServiceDetail = async (contentId) => {
  try {
    loading.value = true
    const result = await getContentDetail(contentId)
    
    if (result && result.data) {
      serviceDetail.value = result.data
    } else {
      serviceDetail.value = {}
      message.error('获取服务详情失败')
    }
  } catch (error) {
    console.error('获取服务详情失败:', error)
    message.error('获取服务详情失败')
    serviceDetail.value = {}
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
.service-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.detail-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.back-btn {
  margin-bottom: 15px;
}

.detail-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  text-align: center;
}

.detail-content {
  padding: 20px;
}

.service-info {
  max-width: 800px;
  margin: 0 auto;
}

.info-meta {
  text-align: center;
  color: #666;
  margin-bottom: 20px;
  font-size: 14px;
}

.service-tags {
  text-align: center;
  margin-bottom: 20px;
}

.service-summary {
  background-color: #f8f8f8;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  border-left: 4px solid var(--primary-color);
}

.summary-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.summary-content {
  color: #666;
  line-height: 1.6;
}

.service-content {
  margin-bottom: 30px;
  line-height: 1.8;
  color: #333;
}

.service-attachments {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.attachments-title {
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.attachment-item {
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.attachment-link {
  color: var(--primary-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.attachment-link:hover {
  color: var(--primary-dark-color);
  text-decoration: underline;
}

.empty-data {
  text-align: center;
  padding: 60px 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .container {
    width: 100%;
    margin: 0 15px;
  }
}

@media (max-width: 768px) {
  .container {
    margin: 0;
    border-radius: 0;
  }
  
  .detail-header {
    padding: 15px;
  }
  
  .detail-content {
    padding: 15px;
  }
  
  .detail-title {
    font-size: 20px;
  }
  
  .info-meta {
    font-size: 12px;
  }
}
</style>

<style>
.service-content img {
  max-width: 100%;
  height: auto;
}

.service-content p {
  margin-bottom: 15px;
}

.service-content h1,
.service-content h2,
.service-content h3,
.service-content h4,
.service-content h5,
.service-content h6 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #333;
}
</style>
