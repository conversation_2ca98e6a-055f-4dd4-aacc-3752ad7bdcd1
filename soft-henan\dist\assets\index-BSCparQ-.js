import{_ as $,r as c,o as q,e as B,v as y,c as n,a,b as r,d,f as k,h as o,F as g,j as b,t as l}from"./index-DIypJWEE.js";const K={class:"info-container"},C={class:"container"},U={class:"info-content"},Y={class:"min-height-container"},M={class:"info-section"},N={class:"info-list"},P={class:"info-title"},R={class:"info-date"},V=["href"],j={class:"info-section"},A={class:"info-list"},E={class:"info-title"},L={class:"info-date"},z=["href"],G={class:"info-section"},H={class:"info-list"},J={class:"info-title"},O={class:"info-date"},Q=["href"],S={__name:"index",setup(W){const h=c(!1),m=c("1"),_=c([]),u=c([]),f=c([]);q(()=>{console.log("信息公开页面已加载"),w()});const w=async()=>{try{h.value=!0,await Promise.all([x(),D(),F()])}catch(e){console.error("获取信息公开数据失败:",e),B.error("获取信息公开数据失败")}finally{h.value=!1}},x=async()=>{try{const e=await y(8,10);e&&e.data?_.value=e.data.map(t=>({title:t.title,date:p(t.publishTime),link:t.attachment&&t.attachment.length>0?t.attachment[0].fileUrl:`/files/annual-report-${new Date(t.publishTime).getFullYear()}.pdf`})):_.value=[]}catch(e){console.error("获取年度报告数据失败:",e),_.value=[]}},D=async()=>{try{const e=await y(9,10);e&&e.data?u.value=e.data.map(t=>({title:t.title,date:p(t.publishTime),link:t.attachment&&t.attachment.length>0?t.attachment[0].fileUrl:`/files/financial-${new Date(t.publishTime).getFullYear()}.pdf`})):u.value=[]}catch(e){console.error("获取财务信息数据失败:",e),u.value=[]}},F=async()=>{try{const e=await y(10,10);e&&e.data?f.value=e.data.map(t=>({title:t.title,date:p(t.publishTime),link:`/policy/detail?id=${t.contentId}`})):f.value=[]}catch(e){console.error("获取政策文件数据失败:",e),f.value=[]}},p=e=>{if(!e)return"";const t=new Date(e);return`${t.getFullYear()}年${t.getMonth()+1}月`};return(e,t)=>{const v=k("a-tab-pane"),T=k("a-tabs"),I=k("a-spin");return o(),n("div",K,[a("div",C,[t[1]||(t[1]=a("h1",{class:"page-title"},"信息公开",-1)),a("div",U,[r(I,{spinning:h.value,tip:"加载中..."},{default:d(()=>[a("div",Y,[r(T,{activeKey:m.value,"onUpdate:activeKey":t[0]||(t[0]=s=>m.value=s)},{default:d(()=>[r(v,{key:"1",tab:"年度报告"},{default:d(()=>[a("div",M,[a("div",N,[(o(!0),n(g,null,b(_.value,(s,i)=>(o(),n("div",{class:"info-item",key:i},[a("h3",P,l(s.title),1),a("p",R,l(s.date),1),a("a",{href:s.link,class:"download-link"},"下载查看",8,V)]))),128))])])]),_:1}),r(v,{key:"2",tab:"财务信息"},{default:d(()=>[a("div",j,[a("div",A,[(o(!0),n(g,null,b(u.value,(s,i)=>(o(),n("div",{class:"info-item",key:i},[a("h3",E,l(s.title),1),a("p",L,l(s.date),1),a("a",{href:s.link,class:"download-link"},"下载查看",8,z)]))),128))])])]),_:1}),r(v,{key:"3",tab:"政策法规"},{default:d(()=>[a("div",G,[a("div",H,[(o(!0),n(g,null,b(f.value,(s,i)=>(o(),n("div",{class:"info-item",key:i},[a("h3",J,l(s.title),1),a("p",O,l(s.date),1),a("a",{href:s.link,class:"download-link"},"查看详情",8,Q)]))),128))])])]),_:1})]),_:1},8,["activeKey"])])]),_:1},8,["spinning"])])])])}}},Z=$(S,[["__scopeId","data-v-17946da7"]]);export{Z as default};
