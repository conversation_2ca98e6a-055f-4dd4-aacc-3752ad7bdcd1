:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 主题颜色CSS变量 - 医学专业主题 */
  --primary-color: #1976d2;
  --primary-dark-color: #1565c0;
  --link-color: #1976d2;
  --secondary-color: #0288d1;
  --accent-color: #00acc1;
  --success-color: #4caf50;
  --background-gradient: linear-gradient(135deg, #1976d2 0%, #0288d1 100%);
  --card-shadow: 0 4px 20px rgba(25, 118, 210, 0.08);
  --hover-shadow: 0 8px 30px rgba(25, 118, 210, 0.15);
}

a {
  font-weight: 500;
  color: var(--link-color, #646cff);
  text-decoration: inherit;
}
a:hover {
  color: var(--link-color, #535bf2);
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  width: 100%;
  margin: 0 auto;
}

/* 全局Ant Design菜单样式覆盖 */
.ant-menu-submenu-popup {
  z-index: 1050 !important;
  position: absolute !important;
}

.ant-menu-submenu-popup .ant-menu {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.ant-menu-submenu-popup .ant-menu-item {
  background-color: transparent !important;
  color: white !important;
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
}

.ant-menu-submenu-popup .ant-menu-item:hover {
  background-color: var(--primary-dark-color) !important;
  color: white !important;
  border-left-color: rgba(255, 255, 255, 0.8);
  transform: translateX(2px);
}

.ant-menu-submenu-popup .ant-menu-item-selected {
  background-color: var(--primary-dark-color) !important;
  color: white !important;
}

.ant-menu-submenu-popup .ant-menu-item a {
  color: white !important;
  text-decoration: none !important;
}

.ant-menu-submenu-popup .ant-menu-item:hover a {
  color: white !important;
}

.ant-menu-submenu-popup .ant-menu-item-selected a {
  color: white !important;
}

/* 移除所有下拉箭头 */
.ant-menu-submenu-title::after {
  display: none !important;
}

.ant-menu-sub .ant-menu-item a::after {
  display: none !important;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
