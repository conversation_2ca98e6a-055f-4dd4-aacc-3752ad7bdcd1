import{_ as I,r as b,o as L,u as V,e as y,p as H,c,a,b as o,d as n,t as i,f as d,s as M,h as s,l as u,x as D,i as r,n as v,F as q,j as A}from"./index-DIypJWEE.js";import{A as O,F as R}from"./FileTextOutlined-BdIGULaK.js";import"./AntdIcon-C5sZj2-2.js";const j={class:"policy-detail-container"},z={class:"container"},E={class:"detail-header"},P={class:"detail-title"},S={class:"detail-content"},U={key:0},$={class:"policy-info"},G={class:"info-meta"},J={class:"policy-tags"},K={key:0,class:"policy-summary"},Q={class:"summary-content"},W=["innerHTML"],X={key:1,class:"policy-attachments"},Y={class:"attachments-list"},Z=["href"],tt={key:1,class:"empty-data"},et={__name:"detail",setup(at){const C=M(),f=V(),_=b(!1),e=b({});L(()=>{const l=f.query.id||f.params.id;l?N(l):y.error("缺少政策内容ID")});const N=async l=>{try{_.value=!0;const t=await H(l);t&&t.data?e.value=t.data:(e.value={},y.error("获取政策详情失败"))}catch(t){console.error("获取政策详情失败:",t),y.error("获取政策详情失败"),e.value={}}finally{_.value=!1}},w=l=>l?new Date(l).toLocaleDateString("zh-CN"):"",x=()=>{C.back()};return(l,t)=>{const F=d("a-button"),m=d("a-divider"),h=d("a-space"),p=d("a-tag"),T=d("a-empty"),B=d("a-spin");return s(),c("div",j,[a("div",z,[a("div",E,[o(F,{onClick:x,class:"back-btn"},{icon:n(()=>[o(D(O))]),default:n(()=>[t[0]||(t[0]=u(" 返回 "))]),_:1}),a("h1",P,i(e.value.title),1)]),a("div",S,[o(B,{spinning:_.value,tip:"加载中..."},{default:n(()=>[!_.value&&e.value.contentId?(s(),c("div",U,[a("div",$,[a("div",G,[o(h,null,{default:n(()=>[a("span",null,"分类："+i(e.value.contentTypeName||"政策法规"),1),o(m,{type:"vertical"}),a("span",null,"作者："+i(e.value.author||"管理员"),1),o(m,{type:"vertical"}),a("span",null,"来源："+i(e.value.source||"本会"),1),o(m,{type:"vertical"}),a("span",null,"发布时间："+i(w(e.value.publishTime)),1),o(m,{type:"vertical"}),a("span",null,"浏览量："+i(e.value.pageViewCount||0),1)]),_:1})]),a("div",J,[o(h,null,{default:n(()=>[e.value.topFlag?(s(),v(p,{key:0,color:"success"},{default:n(()=>t[1]||(t[1]=[u("置顶")])),_:1})):r("",!0),e.value.recommendFlag?(s(),v(p,{key:1,color:"success"},{default:n(()=>t[2]||(t[2]=[u("推荐")])),_:1})):r("",!0),e.value.status===0?(s(),v(p,{key:2,color:"warning"},{default:n(()=>t[3]||(t[3]=[u("草稿")])),_:1})):r("",!0),e.value.status===1?(s(),v(p,{key:3,color:"success"},{default:n(()=>t[4]||(t[4]=[u("已发布")])),_:1})):r("",!0),e.value.status===2?(s(),v(p,{key:4,color:"error"},{default:n(()=>t[5]||(t[5]=[u("已下线")])),_:1})):r("",!0)]),_:1})]),e.value.summary?(s(),c("div",K,[t[6]||(t[6]=a("div",{class:"summary-title"},"摘要：",-1)),a("div",Q,i(e.value.summary),1)])):r("",!0),a("div",{class:"policy-content",innerHTML:e.value.contentHtml},null,8,W),e.value.attachment&&e.value.attachment.length>0?(s(),c("div",X,[t[7]||(t[7]=a("div",{class:"attachments-title"},"附件：",-1)),a("div",Y,[(s(!0),c(q,null,A(e.value.attachment,(k,g)=>(s(),c("div",{key:g,class:"attachment-item"},[a("a",{href:k.fileUrl,target:"_blank",class:"attachment-link"},[o(D(R)),u(" "+i(k.fileName||`附件${g+1}`),1)],8,Z)]))),128))])])):r("",!0)])])):!_.value&&!e.value.contentId?(s(),c("div",tt,[o(T,{description:"政策内容不存在或已被删除"})])):r("",!0)]),_:1},8,["spinning"])])])])}}},lt=I(et,[["__scopeId","data-v-c012e123"]]);export{lt as default};
