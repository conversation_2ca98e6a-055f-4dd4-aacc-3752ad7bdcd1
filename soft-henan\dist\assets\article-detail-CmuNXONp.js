import{_ as y,r as _,o as v,c as k,a as m,b as a,d as o,u as g,e as n,f as s,s as h,h as C,l as c}from"./index-DIypJWEE.js";import{b as D,C as x}from"./detailConfig-CjbJ0jXA.js";const N={class:"branch-article-detail-container"},I={class:"container"},V={class:"breadcrumb"},B={__name:"article-detail",setup(T){const i=g();h();const r=_(!1),d=_({}),f={...D,idField:"contentId",metaFields:[{key:"contentTypeName",label:"分类",formatter:t=>t||"分会管理"},{key:"branchName",label:"所属分会"},{key:"author",label:"作者",formatter:t=>t||"管理员"},{key:"publishTime",label:"发布时间",type:"date"},{key:"pageViewCount",label:"浏览量",formatter:t=>t||0}]},b=async()=>{try{r.value=!0;const t=i.params.id||i.query.id;if(!t){n.error("缺少文章ID参数");return}const e={code:1,data:{contentId:t,title:"文章标题",contentHtml:"文章内容"}};e&&(e.code===0||e.code===1||e.code===200)&&e.data?d.value=e.data:(console.error("获取文章详情失败，响应:",e),n.error("获取文章详情失败"))}catch(t){console.error("获取文章详情失败:",t),n.error("获取文章详情失败: "+(t.message||"未知错误"))}finally{r.value=!1}};return v(()=>{b()}),(t,e)=>{const u=s("router-link"),l=s("a-breadcrumb-item"),p=s("a-breadcrumb");return C(),k("div",N,[m("div",I,[m("div",V,[a(p,null,{default:o(()=>[a(l,null,{default:o(()=>[a(u,{to:"/"},{default:o(()=>e[0]||(e[0]=[c("首页")])),_:1})]),_:1}),a(l,null,{default:o(()=>[a(u,{to:"/branch"},{default:o(()=>e[1]||(e[1]=[c("分会")])),_:1})]),_:1}),a(l,null,{default:o(()=>e[2]||(e[2]=[c("文章详情")])),_:1})]),_:1})]),a(x,{detail:d.value,loading:r.value,config:f},null,8,["detail","loading"])])])}}},F=y(B,[["__scopeId","data-v-c3d6228f"]]);export{F as default};
