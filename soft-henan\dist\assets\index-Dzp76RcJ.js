import{_ as b,r as p,o as D,c as o,a as t,b as h,d as T,v as $,e as B,f as v,s as F,h as i,i as g,F as m,j as y,t as c}from"./index-DIypJWEE.js";const L={class:"representative-page"},I={class:"container"},P={class:"page-content"},V={class:"min-height-container"},R={class:"representative-list"},j={class:"item-info"},q={class:"item-title"},E={class:"item-meta"},J={key:0,class:"summary-content"},M={key:1},O={class:"item-director"},Q={class:"item-time"},U={class:"item-phone"},A={key:2,class:"new-tag"},G={class:"item-actions"},H=["onClick"],K={key:0,class:"pagination-container"},W={__name:"index",setup(X){const u=p(!1),l=p([]),f=F(),s=p({current:1,pageSize:10,total:0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(a,e)=>`第 ${e[0]}-${e[1]} 条，共 ${a} 条数据`}),S=a=>a?new Date(a).toLocaleDateString("zh-CN"):"待定",d=async()=>{try{u.value=!0;const a={pageNum:s.value.current,pageSize:s.value.pageSize,status:1,deletedFlag:!1},e=await $(11,s.value.pageSize);e&&e.data?(l.value=e.data.map(r=>({title:r.title,director:r.author||"待定",establishTime:S(r.publishTime),contactPhone:r.summary||"待定",isNew:r.recommendFlag||!1,contentId:r.contentId})),s.value.total=e.data.length):(l.value=[],s.value.total=0)}catch(a){console.error("获取代表处列表失败:",a),B.error("获取代表处列表失败，请稍后重试"),l.value=[],s.value.total=0}finally{u.value=!1}},w=a=>{f.push({path:`/representative/detail/${a.id}`,query:{title:a.title}})},z=a=>{s.value.current=a,d()},C=(a,e)=>{s.value.current=1,s.value.pageSize=e,d()};return D(()=>{d()}),(a,e)=>{const r=v("a-pagination"),k=v("a-spin");return i(),o("div",L,[t("div",I,[e[2]||(e[2]=t("h1",{class:"page-title"},"代表处",-1)),t("div",P,[h(k,{spinning:u.value,tip:"加载中..."},{default:T(()=>[t("div",V,[e[1]||(e[1]=t("div",{class:"intro-text"},[t("p",null,"河南省软组织病研究会下设各分会，分为理事会分会和各分支机构分会，促进学科发展。")],-1)),t("div",R,[(i(!0),o(m,null,y(l.value,(n,x)=>(i(),o("div",{key:x,class:"representative-item"},[t("div",j,[t("h3",q,c(n.title),1),t("div",E,[n.rawSummary?(i(),o("div",J,[(i(!0),o(m,null,y(n.rawSummary.split("||"),(_,N)=>(i(),o("p",{key:N,class:"summary-line"},c(_.trim()),1))),128))])):(i(),o("div",M,[t("span",O,"负责人："+c(n.director),1),t("span",Q,"成立时间："+c(n.establishTime),1),t("span",U,"联系电话："+c(n.contactPhone),1)])),n.isNew?(i(),o("span",A,"新成立")):g("",!0)])]),t("div",G,[t("a",{onClick:_=>w(n),class:"detail-btn"},"查看详情",8,H)])]))),128))]),l.value.length>0?(i(),o("div",K,[h(r,{current:s.value.current,"onUpdate:current":e[0]||(e[0]=n=>s.value.current=n),total:s.value.total,pageSize:s.value.pageSize,onChange:z,showSizeChanger:"",pageSizeOptions:["5","10","20"],onShowSizeChange:C},null,8,["current","total","pageSize"])])):g("",!0)])]),_:1},8,["spinning"])])])])}}},Z=b(W,[["__scopeId","data-v-06548cd8"]]);export{Z as default};
