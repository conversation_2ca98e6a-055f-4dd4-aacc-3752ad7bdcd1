import{_ as c,r as n,o as u,n as d,x as f,u as p,e as o,p as _,s as g,h as m}from"./index-DIypJWEE.js";import{g as y,C as v}from"./detailConfig-CjbJ0jXA.js";const D={__name:"detail",setup(C){const r=p();g();const t=n(!1),s=n({}),l=y("party"),i=async()=>{try{t.value=!0;const e=r.params.id||r.query.id;if(!e){o.error("缺少党建动态ID参数");return}const a=await _(e);a&&a.code===0&&a.data?s.value=a.data:o.error((a==null?void 0:a.msg)||"获取党建动态详情失败")}catch(e){console.error("获取党建动态详情失败:",e),o.error("获取党建动态详情失败")}finally{t.value=!1}};return u(()=>{i()}),(e,a)=>(m(),d(v,{detail:s.value,loading:t.value,config:f(l)},null,8,["detail","loading","config"]))}},k=c(D,[["__scopeId","data-v-88a8d9c0"]]);export{k as default};
