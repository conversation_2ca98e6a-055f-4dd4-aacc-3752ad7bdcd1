import{C as n}from"./index-DIypJWEE.js";function r(t){return console.log("调用API: 提交报名信息",t),n.post("/portal/registration/submit",t).then(o=>(console.log("提交报名信息成功:",o),o)).catch(o=>{throw console.error("提交报名信息失败:",o),o})}function c(t){return console.log("调用API: 验证支付状态",t),n.get(`/portal/registration/payment/verify/${t}`).then(o=>(console.log("验证支付状态成功:",o),o)).catch(o=>{throw console.error("验证支付状态失败:",o),o})}function l(t){return console.log("调用API: 生成支付二维码",t),n.post("/portal/registration/payment/qrcode",t).then(o=>(console.log("生成支付二维码成功:",o),o)).catch(o=>{throw console.error("生成支付二维码失败:",o),o})}function s(t){return console.log("调用API: 生成微信 JSAPI 支付参数",t),n.post("/portal/registration/payment/jsapi",t).then(o=>(console.log("生成微信 JSAPI 支付参数成功:",o),o)).catch(o=>{throw console.error("生成微信 JSAPI 支付参数失败:",o),o})}function a(){return console.log("调用API: 获取活动费用信息"),n.get("/portal/registration/fee").then(t=>(console.log("获取活动费用信息成功:",t),t)).catch(t=>{throw console.error("获取活动费用信息失败:",t),t})}export{l as a,s as b,a as g,r as s,c as v};
