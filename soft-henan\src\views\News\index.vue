<template>
  <div class="news-container">
    <div class="container">
      <h1 class="page-title">新闻资讯</h1>
      <div class="news-content">
        <a-spin :spinning="loading" tip="加载中...">
          <div class="min-height-container">
            <div class="news-list" v-if="newsData.length > 0">
              <div class="news-item" v-for="(item, index) in newsData" :key="index">
                <div class="news-image">
                  <img v-if="item.image" :src="item.image" :alt="item.title" />
                </div>
                <div class="news-info">
                  <div class="news-date">{{ formatDate(item.publishTime) }}</div>
                  <h3 class="news-title">{{ item.title }}</h3>
                  <p class="news-desc">{{ item.summary || truncateText(item.contentText) }}</p>
                  <router-link :to="{ path: `/news/detail/${item.contentId}` }" class="read-more">阅读更多</router-link>
                </div>
              </div>
            </div>
            <div class="empty-data" v-else>
              <a-empty description="暂无数据" />
            </div>

            <div class="pagination">
              <a-pagination
                v-model:current="pagination.current"
                :total="pagination.total"
                :pageSize="pagination.pageSize"
                @change="handlePageChange"
                showSizeChanger
                :pageSizeOptions="['5', '10', '20']"
                @showSizeChange="handleSizeChange"
              />
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { getAllContentTypes, queryContent } from '../../api/content'

// 响应式数据
const loading = ref(false)
const newsData = ref([])
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

// 页面加载
onMounted(() => {
  console.log('新闻页面已加载')
  queryNewsData()
})

// 查询新闻数据
const queryNewsData = async () => {
  try {
    loading.value = true
    // 使用getAllContentTypes和queryContent来获取新闻内容
    const typesRes = await getAllContentTypes()
    console.log('获取内容类型结果:', typesRes)
    
    if (typesRes && (typesRes.code === 0 || typesRes.code === 1 || typesRes.code === 200) && typesRes.data) {
      // 查找新闻类型（使用精确的类型编码）
      const newsType = typesRes.data.find(type => 
        type.contentTypeCode === 'news'
      )
      
      console.log('找到的新闻类型:', newsType)
      
      if (newsType) {
        // 使用内容类型ID查询新闻内容
        const params = {
          contentTypeId: newsType.contentTypeId,
          pageNum: pagination.value.current,
          pageSize: pagination.value.pageSize,
          status: 1, // 已发布状态
          deletedFlag: false // 未删除
        }
        
        console.log('查询新闻参数:', params)
        
        const contentRes = await queryContent(params)
        console.log('获取新闻结果:', contentRes)
        
        if (contentRes && (contentRes.code === 0 || contentRes.code === 1 || contentRes.code === 200) && contentRes.data && contentRes.data.list) {
          newsData.value = contentRes.data.list.map(item => ({
            title: item.title,
            image: item.coverImage,
            publishTime: item.publishTime,
            summary: item.summary,
            contentText: item.contentText,
            contentId: item.contentId
          }))
          pagination.value.total = contentRes.data.total || 0
          console.log('获取到的新闻数据:', newsData.value)
        } else {
          console.warn('获取新闻数据失败或无数据:', contentRes)
          newsData.value = []
          pagination.value.total = 0
        }
      } else {
        console.warn('未找到news内容类型，请检查后台内容类型配置')
        newsData.value = []
        pagination.value.total = 0
      }
    } else {
      console.warn('获取内容类型失败或内容类型数据为空:', typesRes)
      newsData.value = []
      pagination.value.total = 0
    }
  } catch (error) {
    console.error('获取新闻数据失败:', error)
    message.error('获取新闻数据失败')
    newsData.value = []
    pagination.value.total = 0
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const handlePageChange = (page, pageSize) => {
  pagination.value.current = page
  pagination.value.pageSize = pageSize
  queryNewsData()
}

// 处理每页条数变化
const handleSizeChange = (current, size) => {
  pagination.value.current = 1
  pagination.value.pageSize = size
  queryNewsData()
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 截断文本
const truncateText = (text, maxLength = 100) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}
</script>

<style scoped>
.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0 40px;
}

.page-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--primary-color);
}

.min-height-container {
  min-height: 400px;
}

.news-content {
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.news-item {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  padding: 20px;
}

.news-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.news-image {
  width: 120px;
  height: 90px;
  flex-shrink: 0;
  overflow: hidden;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-image::before {
  content: "📰";
  font-size: 24px;
  color: #ccc;
}

.news-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.news-date {
  color: #999;
  font-size: 12px;
  margin-bottom: 8px;
}

.news-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

.news-desc {
  color: #777;
  line-height: 1.5;
  margin-bottom: 8px;
  font-size: 13px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.read-more {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.read-more:hover {
  color: var(--primary-dark-color);
  text-decoration: underline;
}

.empty-data {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.pagination {
  margin-top: 30px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    width: 100%;
    padding: 15px;
  }
  
  .news-item {
    flex-direction: column;
    padding: 15px;
  }
  
  .news-image {
    width: 100px;
    height: 75px;
    margin-right: 10px;
    margin-bottom: 10px;
  }
}
</style>