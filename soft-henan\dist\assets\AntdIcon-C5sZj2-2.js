import{E as V,r as b,G as F,H as I,I as X,J as Z,K,M as nn,b as v,N as en}from"./index-DIypJWEE.js";var rn=Symbol("iconContext"),N=function(){return V(rn,{prefixCls:b("anticon"),rootClassName:b(""),csp:b()})};function w(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function tn(n,r){return n&&n.contains?n.contains(r):!1}var A="data-vc-order",on="vc-icon-key",C=new Map;function R(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=n.mark;return r?r.startsWith("data-")?r:"data-".concat(r):on}function O(n){if(n.attachTo)return n.attachTo;var r=document.querySelector("head");return r||document.body}function an(n){return n==="queue"?"prependQueue":n?"prepend":"append"}function L(n){return Array.from((C.get(n)||n).children).filter(function(r){return r.tagName==="STYLE"})}function M(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!w())return null;var e=r.csp,o=r.prepend,t=document.createElement("style");t.setAttribute(A,an(o)),e&&e.nonce&&(t.nonce=e.nonce),t.innerHTML=n;var a=O(r),i=a.firstChild;if(o){if(o==="queue"){var c=L(a).filter(function(l){return["prepend","prependQueue"].includes(l.getAttribute(A))});if(c.length)return a.insertBefore(t,c[c.length-1].nextSibling),t}a.insertBefore(t,i)}else a.appendChild(t);return t}function cn(n){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=O(r);return L(e).find(function(o){return o.getAttribute(R(r))===n})}function ln(n,r){var e=C.get(n);if(!e||!tn(document,e)){var o=M("",r),t=o.parentNode;C.set(n,t),n.removeChild(o)}}function un(n,r){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=O(e);ln(o,e);var t=cn(r,e);if(t)return e.csp&&e.csp.nonce&&t.nonce!==e.csp.nonce&&(t.nonce=e.csp.nonce),t.innerHTML!==n&&(t.innerHTML=n),t;var a=M(n,e);return a.setAttribute(R(e),r),a}function x(n){for(var r=1;r<arguments.length;r++){var e=arguments[r]!=null?Object(arguments[r]):{},o=Object.keys(e);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(e).filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.forEach(function(t){sn(n,t,e[t])})}return n}function sn(n,r,e){return r in n?Object.defineProperty(n,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[r]=e,n}function P(n){return typeof n=="object"&&typeof n.name=="string"&&typeof n.theme=="string"&&(typeof n.icon=="object"||typeof n.icon=="function")}function h(n,r,e){return e?I(n.tag,x({key:r},e,n.attrs),(n.children||[]).map(function(o,t){return h(o,"".concat(r,"-").concat(n.tag,"-").concat(t))})):I(n.tag,x({key:r},n.attrs),(n.children||[]).map(function(o,t){return h(o,"".concat(r,"-").concat(n.tag,"-").concat(t))}))}function z(n){return F(n)[0]}function B(n){return n?Array.isArray(n)?n:[n]:[]}var fn=`
.anticon {
  display: inline-block;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`;function D(n){return n&&n.getRootNode&&n.getRootNode()}function pn(n){return w()?D(n)instanceof ShadowRoot:!1}function dn(n){return pn(n)?D(n):null}var yn=function(){var r=N(),e=r.prefixCls,o=r.csp,t=Z(),a=fn;e&&(a=a.replace(/anticon/g,e.value)),X(function(){if(w()){var i=t.vnode.el,c=dn(i);un(a,"@ant-design-vue-icons",{prepend:!0,csp:o.value,attachTo:c})}})},mn=["icon","primaryColor","secondaryColor"];function gn(n,r){if(n==null)return{};var e=bn(n,r),o,t;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(n);for(t=0;t<a.length;t++)o=a[t],!(r.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(n,o)&&(e[o]=n[o])}return e}function bn(n,r){if(n==null)return{};var e={},o=Object.keys(n),t,a;for(a=0;a<o.length;a++)t=o[a],!(r.indexOf(t)>=0)&&(e[t]=n[t]);return e}function y(n){for(var r=1;r<arguments.length;r++){var e=arguments[r]!=null?Object(arguments[r]):{},o=Object.keys(e);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(e).filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.forEach(function(t){vn(n,t,e[t])})}return n}function vn(n,r,e){return r in n?Object.defineProperty(n,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[r]=e,n}var p=K({primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1});function Cn(n){var r=n.primaryColor,e=n.secondaryColor;p.primaryColor=r,p.secondaryColor=e||z(r),p.calculated=!!e}function hn(){return y({},p)}var s=function(r,e){var o=y({},r,e.attrs),t=o.icon,a=o.primaryColor,i=o.secondaryColor,c=gn(o,mn),l=p;if(a&&(l={primaryColor:a,secondaryColor:i||z(a)}),P(t),!P(t))return null;var u=t;return u&&typeof u.icon=="function"&&(u=y({},u,{icon:u.icon(l.primaryColor,l.secondaryColor)})),h(u.icon,"svg-".concat(u.name),y({},c,{"data-icon":u.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"}))};s.props={icon:Object,primaryColor:String,secondaryColor:String,focusable:String};s.inheritAttrs=!1;s.displayName="IconBase";s.getTwoToneColors=hn;s.setTwoToneColors=Cn;function wn(n,r){return Sn(n)||_n(n,r)||Tn(n,r)||On()}function On(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Tn(n,r){if(n){if(typeof n=="string")return k(n,r);var e=Object.prototype.toString.call(n).slice(8,-1);if(e==="Object"&&n.constructor&&(e=n.constructor.name),e==="Map"||e==="Set")return Array.from(n);if(e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return k(n,r)}}function k(n,r){(r==null||r>n.length)&&(r=n.length);for(var e=0,o=new Array(r);e<r;e++)o[e]=n[e];return o}function _n(n,r){var e=n==null?null:typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(e!=null){var o=[],t=!0,a=!1,i,c;try{for(e=e.call(n);!(t=(i=e.next()).done)&&(o.push(i.value),!(r&&o.length===r));t=!0);}catch(l){a=!0,c=l}finally{try{!t&&e.return!=null&&e.return()}finally{if(a)throw c}}return o}}function Sn(n){if(Array.isArray(n))return n}function W(n){var r=B(n),e=wn(r,2),o=e[0],t=e[1];return s.setTwoToneColors({primaryColor:o,secondaryColor:t})}function jn(){var n=s.getTwoToneColors();return n.calculated?[n.primaryColor,n.secondaryColor]:n.primaryColor}var In=nn({name:"InsertStyles",setup:function(){return yn(),function(){return null}}}),An=["class","icon","spin","rotate","tabindex","twoToneColor","onClick"];function xn(n,r){return En(n)||$n(n,r)||kn(n,r)||Pn()}function Pn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kn(n,r){if(n){if(typeof n=="string")return $(n,r);var e=Object.prototype.toString.call(n).slice(8,-1);if(e==="Object"&&n.constructor&&(e=n.constructor.name),e==="Map"||e==="Set")return Array.from(n);if(e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return $(n,r)}}function $(n,r){(r==null||r>n.length)&&(r=n.length);for(var e=0,o=new Array(r);e<r;e++)o[e]=n[e];return o}function $n(n,r){var e=n==null?null:typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(e!=null){var o=[],t=!0,a=!1,i,c;try{for(e=e.call(n);!(t=(i=e.next()).done)&&(o.push(i.value),!(r&&o.length===r));t=!0);}catch(l){a=!0,c=l}finally{try{!t&&e.return!=null&&e.return()}finally{if(a)throw c}}return o}}function En(n){if(Array.isArray(n))return n}function E(n){for(var r=1;r<arguments.length;r++){var e=arguments[r]!=null?Object(arguments[r]):{},o=Object.keys(e);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(e).filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.forEach(function(t){f(n,t,e[t])})}return n}function f(n,r,e){return r in n?Object.defineProperty(n,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[r]=e,n}function Nn(n,r){if(n==null)return{};var e=Rn(n,r),o,t;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(n);for(t=0;t<a.length;t++)o=a[t],!(r.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(n,o)&&(e[o]=n[o])}return e}function Rn(n,r){if(n==null)return{};var e={},o=Object.keys(n),t,a;for(a=0;a<o.length;a++)t=o[a],!(r.indexOf(t)>=0)&&(e[t]=n[t]);return e}W(en.primary);var d=function(r,e){var o,t=E({},r,e.attrs),a=t.class,i=t.icon,c=t.spin,l=t.rotate,u=t.tabindex,H=t.twoToneColor,T=t.onClick,q=Nn(t,An),_=N(),m=_.prefixCls,S=_.rootClassName,U=(o={},f(o,S.value,!!S.value),f(o,m.value,!0),f(o,"".concat(m.value,"-").concat(i.name),!!i.name),f(o,"".concat(m.value,"-spin"),!!c||i.name==="loading"),o),g=u;g===void 0&&T&&(g=-1);var Q=l?{msTransform:"rotate(".concat(l,"deg)"),transform:"rotate(".concat(l,"deg)")}:void 0,Y=B(H),j=xn(Y,2),G=j[0],J=j[1];return v("span",E({role:"img","aria-label":i.name},q,{onClick:T,class:[U,a],tabindex:g}),[v(s,{icon:i,primaryColor:G,secondaryColor:J,style:Q},null),v(In,null,null)])};d.props={spin:Boolean,rotate:Number,icon:Object,twoToneColor:[String,Array]};d.displayName="AntdIcon";d.inheritAttrs=!1;d.getTwoToneColor=jn;d.setTwoToneColor=W;export{d as I};
