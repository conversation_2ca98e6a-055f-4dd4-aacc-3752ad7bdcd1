import{p as v,_ as W,r as b,o as y,c as D,a as h,b as l,d as c,u as w,e as f,f as X,s as x,h as z,l as g,t as C}from"./index-DIypJWEE.js";import{r as k,C as M}from"./detailConfig-CjbJ0jXA.js";function T(t){if(t&&t.includes("负责人")&&t.includes("成立时间")&&t.includes("联系电话")&&t.includes("办公地址")&&t.includes("电子邮箱"))return{director:"张明",establishTime:"",contactPhone:"0371-XXXXXXXX",address:"河南省郑州市金水区文化路97号河南省人民医院",email:"<EMAIL>"};const a=t.split("||"),s={director:"张明",establishTime:"",contactPhone:"0371-XXXXXXXX",address:"河南省郑州市金水区文化路97号河南省人民医院",email:"<EMAIL>"};return a.forEach(o=>{var u,m,n,i,d;const e=o.trim();if(e.startsWith("负责人:")||e.startsWith("负责人：")||e.startsWith("主任委员:")||e.startsWith("主任委员：")){const r=(u=e.split(/[：:]/)[1])==null?void 0:u.trim();r&&(s.director=r)}else if(e.startsWith("成立时间:")||e.startsWith("成立时间：")||e.includes("成立于")){let r="";if(e.includes("成立于")){const p=e.match(/成立于\s*([0-9]{4}[年\s\./-][0-9]{1,2}[月]?)/);r=p?p[1]:""}else r=(m=e.split(/[：:]/)[1])==null?void 0:m.trim();r&&(s.establishTime=r)}else if(e.startsWith("电话:")||e.startsWith("电话：")||e.startsWith("联系电话:")||e.startsWith("联系电话：")){const r=(n=e.split(/[：:]/)[1])==null?void 0:n.trim();r&&(s.contactPhone=r)}else if(e.startsWith("地址:")||e.startsWith("地址：")||e.startsWith("办公地址:")||e.startsWith("办公地址：")){const r=(i=e.split(/[：:]/)[1])==null?void 0:i.trim();r&&(s.address=r)}else if(e.startsWith("邮箱:")||e.startsWith("邮箱：")||e.startsWith("电子邮箱:")||e.startsWith("电子邮箱：")){const r=(d=e.split(/[：:]/)[1])==null?void 0:d.trim();r&&(s.email=r)}}),s}function P(t){return v(t).then(a=>{var s;if(console.log("获取代表处详情结果:",a),a&&(a.code===0||a.code===1)&&a.data){const o=T(a.data.summary||"");if(o.address==="河南省郑州市金水区文化路97号河南省人民医院"){const e=I(a.data.contentHtml||"");e&&(o.address=e)}return o.email==="<EMAIL>"&&(o.email=R(a.data.title||"")),{code:a.code,data:{id:a.data.contentId,title:a.data.title,description:a.data.summary||"",rawSummary:a.data.summary||"",content:a.data.contentHtml||"",director:o.director,establishTime:o.establishTime||((s=a.data.createTime)==null?void 0:s.substring(0,7))||"",contactPhone:o.contactPhone,address:o.address,email:o.email,viewCount:a.data.pageViewCount||Math.floor(Math.random()*300)+100,publishDate:a.data.createTime||"2024-05-20",source:"原创",author:"管理员"},msg:a.msg}}return _(t)}).catch(a=>(console.error("获取代表处详情失败:",a),_(t)))}function I(t){const a=t.match(/地址[：:]\s*([^<]+)</);return a?a[1].trim():""}function R(t){const a=t.match(/([^会]{2,4}(?:分会))/),s=a?a[1].replace(/分会/,""):"office";return`${{肝脏:"liver",疼痛:"pain",微创:"minimally",心脏:"heart",肺病:"lung",骨科:"ortho",神经:"neuro"}[s]||"office"}@hnrzb.org`}function _(t){return{code:1,data:{id:t,title:t==1?"河南省软组织病研究会肝脏分会":t==2?"河南省软组织病研究会疼痛分会":"河南省软组织病研究会微创技术分会",description:t==1?"专注于肝脏相关软组织病的学术研究、技术推广与交流，致力于提高软组织肝脏疾病的诊疗水平，推动学科发展。":t==2?"以软组织疼痛性疾病研究为核心，探索疼痛机制与治疗术，促进临床转化与应用，提高患者生活质量。":"致力于软组织疾病微创治疗技术的研发与推广，提高微创手术水平，减轻患者痛苦，促进快速康复。",rawSummary:t==1?"负责人：张明||成立时间：2024年05月||联系电话：0371-XXXXXXXX||办公地址：河南省郑州市金水区文化路97号河南省人民医院||电子邮箱：<EMAIL>":t==2?"负责人：赵刚||成立时间：2023年10月||联系电话：0371-XXXXXXXX||办公地址：河南省郑州市中原区建设西路28号郑州大学第二附属医院||电子邮箱：<EMAIL>":"负责人：李华||成立时间：2022年08月||联系电话：0371-XXXXXXXX||办公地址：河南省郑州市二七区康复前街7号河南省中医院||电子邮箱：<EMAIL>",content:t==1?`<p>河南省软组织病研究会肝脏分会成立于2024年5月，是经河南省软组织病研究会批准成立的专业学术组织。</p>
        <p>本分会致力于肝脏相关软组织病的基础与临床研究，推动肝脏软组织疾病诊疗技术的创新与应用，提高我省肝脏软组织疾病的诊疗水平。</p>
        <p>分会主要工作包括：</p>
        <ol>
          <li>开展肝脏软组织疾病的基础与临床研究</li>
          <li>组织学术交流活动，推广先进诊疗技术</li>
          <li>培养专业人才，提高诊疗水平</li>
          <li>促进国内外学术交流与合作</li>
        </ol>`:t==2?`<p>河南省软组织病研究会疼痛分会成立于2023年10月，是经河南省软组织病研究会批准成立的专业学术组织。</p>
        <p>本分会以软组织疼痛性疾病研究为核心，致力于探索疼痛发生机制与治疗方法，促进基础研究向临床应用转化，提高患者生活质量。</p>
        <p>分会主要工作包括：</p>
        <ol>
          <li>开展软组织疼痛性疾病的基础与临床研究</li>
          <li>研发新型疼痛治疗技术与方法</li>
          <li>组织学术交流活动，推广先进诊疗技术</li>
          <li>培养专业人才，提高疼痛诊疗水平</li>
        </ol>`:`<p>河南省软组织病研究会微创技术分会成立于2022年8月，是经河南省软组织病研究会批准成立的专业学术组织。</p>
        <p>本分会致力于软组织疾病微创治疗技术的研发与推广，提高微创手术水平，减轻患者痛苦，促进快速康复。</p>
        <p>分会主要工作包括：</p>
        <ol>
          <li>开展软组织疾病微创治疗技术的基础与临床研究</li>
          <li>研发新型微创治疗设备与方法</li>
          <li>组织学术交流活动，推广先进微创技术</li>
          <li>培养专业人才，提高微创手术水平</li>
        </ol>`,director:t==1?"张明":t==2?"赵刚":"李华",establishTime:t==1?"2024年05月":t==2?"2023年10月":"2022年08月",contactPhone:"0371-XXXXXXXX",address:t==1?"河南省郑州市金水区文化路97号河南省人民医院":t==2?"河南省郑州市中原区建设西路28号郑州大学第二附属医院":"河南省郑州市二七区康复前街7号河南省中医院",email:t==1?"<EMAIL>":t==2?"<EMAIL>":"<EMAIL>",viewCount:Math.floor(Math.random()*300)+100,publishDate:"2024-05-20",source:"原创",author:"管理员"},msg:"操作成功(默认数据)"}}const F={class:"representative-detail-page"},H={class:"container"},S={class:"detail-breadcrumb"},V={class:"page-content"},B={__name:"detail",setup(t){const a=w(),s=x(),o=b(!1),e=b({}),u={...k,contentField:"content",backHandler:()=>{s.push("/representative")}},m=async()=>{try{o.value=!0;const n=a.params.id||a.query.id;if(!n){f.error("缺少代表处ID参数");return}const i=await P(n);i&&(i.code===0||i.code===1||i.code===200)&&i.data?(!i.data.content&&i.data.description&&(i.data.content=i.data.description),e.value=i.data):(console.error("获取代表处详情失败，响应:",i),f.error("获取代表处详情失败"))}catch(n){console.error("获取代表处详情失败:",n),f.error("获取代表处详情失败: "+(n.message||"未知错误"))}finally{o.value=!1}};return y(()=>{m()}),(n,i)=>{const d=X("router-link"),r=X("a-breadcrumb-item"),p=X("a-breadcrumb");return z(),D("div",F,[h("div",H,[i[2]||(i[2]=h("h1",{class:"page-title"},"分支机构",-1)),h("div",S,[l(p,null,{default:c(()=>[l(r,null,{default:c(()=>[l(d,{to:"/"},{default:c(()=>i[0]||(i[0]=[g("首页")])),_:1})]),_:1}),l(r,null,{default:c(()=>[l(d,{to:"/representative"},{default:c(()=>i[1]||(i[1]=[g("代表处")])),_:1})]),_:1}),l(r,null,{default:c(()=>[g(C(e.value.title||"详情"),1)]),_:1})]),_:1})]),h("div",V,[l(M,{detail:e.value,loading:o.value,config:u},null,8,["detail","loading"])])])])}}},q=W(B,[["__scopeId","data-v-85c2a6c3"]]);export{q as default};
