import{_ as C,r as v,o as k,m as x,q as I,e as N,c as r,a as o,b as u,d as m,f as p,h as d,F as b,j as D,i as V,t as h,l as q}from"./index-DIypJWEE.js";const B={class:"news-container"},F={class:"container"},R={class:"news-content"},j={class:"min-height-container"},A={key:0,class:"news-list"},E={class:"news-image"},L=["src","alt"],M={class:"news-info"},O={class:"news-date"},P={class:"news-title"},U={class:"news-desc"},$={key:1,class:"empty-data"},G={class:"pagination"},H={__name:"index",setup(J){const _=v(!1),l=v([]),a=v({current:1,pageSize:10,total:0});k(()=>{console.log("新闻页面已加载"),g()});const g=async()=>{try{_.value=!0;const e=await x();if(console.log("获取内容类型结果:",e),e&&(e.code===0||e.code===1||e.code===200)&&e.data){const t=e.data.find(i=>i.contentTypeCode==="news");if(console.log("找到的新闻类型:",t),t){const i={contentTypeId:t.contentTypeId,pageNum:a.value.current,pageSize:a.value.pageSize,status:1,deletedFlag:!1};console.log("查询新闻参数:",i);const n=await I(i);console.log("获取新闻结果:",n),n&&(n.code===0||n.code===1||n.code===200)&&n.data&&n.data.list?(l.value=n.data.list.map(c=>({title:c.title,image:c.coverImage,publishTime:c.publishTime,summary:c.summary,contentText:c.contentText,contentId:c.contentId})),a.value.total=n.data.total||0,console.log("获取到的新闻数据:",l.value)):(console.warn("获取新闻数据失败或无数据:",n),l.value=[],a.value.total=0)}else console.warn("未找到news内容类型，请检查后台内容类型配置"),l.value=[],a.value.total=0}else console.warn("获取内容类型失败或内容类型数据为空:",e),l.value=[],a.value.total=0}catch(e){console.error("获取新闻数据失败:",e),N.error("获取新闻数据失败"),l.value=[],a.value.total=0}finally{_.value=!1}},w=(e,t)=>{a.value.current=e,a.value.pageSize=t,g()},f=(e,t)=>{a.value.current=1,a.value.pageSize=t,g()},y=e=>e?new Date(e).toLocaleDateString("zh-CN"):"",S=(e,t=100)=>e?e.length<=t?e:e.substring(0,t)+"...":"";return(e,t)=>{const i=p("router-link"),n=p("a-empty"),c=p("a-pagination"),z=p("a-spin");return d(),r("div",B,[o("div",F,[t[2]||(t[2]=o("h1",{class:"page-title"},"新闻资讯",-1)),o("div",R,[u(z,{spinning:_.value,tip:"加载中..."},{default:m(()=>[o("div",j,[l.value.length>0?(d(),r("div",A,[(d(!0),r(b,null,D(l.value,(s,T)=>(d(),r("div",{class:"news-item",key:T},[o("div",E,[s.image?(d(),r("img",{key:0,src:s.image,alt:s.title},null,8,L)):V("",!0)]),o("div",M,[o("div",O,h(y(s.publishTime)),1),o("h3",P,h(s.title),1),o("p",U,h(s.summary||S(s.contentText)),1),u(i,{to:{path:`/news/detail/${s.contentId}`},class:"read-more"},{default:m(()=>t[1]||(t[1]=[q("阅读更多")])),_:2},1032,["to"])])]))),128))])):(d(),r("div",$,[u(n,{description:"暂无数据"})])),o("div",G,[u(c,{current:a.value.current,"onUpdate:current":t[0]||(t[0]=s=>a.value.current=s),total:a.value.total,pageSize:a.value.pageSize,onChange:w,showSizeChanger:"",pageSizeOptions:["5","10","20"],onShowSizeChange:f},null,8,["current","total","pageSize"])])])]),_:1},8,["spinning"])])])])}}},Q=C(H,[["__scopeId","data-v-d65fb3e8"]]);export{Q as default};
