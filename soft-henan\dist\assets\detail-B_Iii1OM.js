import{r as s,o as c,n as u,x as d,u as f,e as t,s as g,h as m}from"./index-DIypJWEE.js";import{a as p,C as _}from"./detailConfig-CjbJ0jXA.js";const y={__name:"detail",setup(v){const r=f();g();const o=s(!1),n=s({}),l=p,i=async()=>{try{o.value=!0;const a=r.params.id||r.query.id;if(!a){t.error("缺少法规ID参数");return}const e={code:1,data:{id:a,title:"法规标题",contentHtml:"法规内容"}};e&&(e.code===0||e.code===1||e.code===200)&&e.data?n.value=e.data:(console.error("获取法规详情失败，响应:",e),t.error("获取法规详情失败"))}catch(a){console.error("获取法规详情失败:",a),t.error("获取法规详情失败: "+(a.message||"未知错误"))}finally{o.value=!1}};return c(()=>{i()}),(a,e)=>(m(),u(_,{detail:n.value,loading:o.value,config:d(l)},null,8,["detail","loading","config"]))}};export{y as default};
