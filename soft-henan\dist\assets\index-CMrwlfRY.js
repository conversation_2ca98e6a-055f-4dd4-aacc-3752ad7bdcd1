import{_ as C,r as v,o as N,m as w,q as I,e as g,c as l,a as t,i as T,b as _,F as x,j as q,f as h,s as B,h as i,t as m}from"./index-DIypJWEE.js";const D={class:"service-container"},F={class:"container"},R={class:"service-content"},V={class:"min-height-container"},b={key:0,class:"loading-container"},P={key:1,class:"empty-data"},$={key:2,class:"service-grid"},j={class:"card-image"},A=["src","alt"],E={key:1,class:"default-icon"},L={class:"card-content"},M={class:"card-title"},O={class:"card-desc"},U=["onClick"],G={key:3,class:"pagination-container"},H={__name:"index",setup(J){const y=B(),u=v(!1),o=v([]),n=v({pageNum:1,pageSize:10}),r=v(0);N(()=>{console.log("服务中心页面已加载"),p()});const p=async()=>{try{u.value=!0;const e=await w();if(console.log("获取内容类型结果:",e),e&&(e.code===0||e.code===1||e.code===200)&&e.data){const a=e.data.find(d=>d.contentTypeCode==="service");if(console.log("找到的服务类型:",a),a){const d={contentTypeId:a.contentTypeId,pageNum:n.value.pageNum,pageSize:n.value.pageSize,status:1,deletedFlag:!1};console.log("查询服务参数:",d);const s=await I(d);console.log("获取服务结果:",s),s&&(s.code===0||s.code===1||s.code===200)&&s.data&&s.data.list?(o.value=s.data.list,r.value=s.data.total||0,console.log("获取到的服务数据:",o.value)):(console.warn("获取服务数据失败或无数据:",s),o.value=[],r.value=0)}else console.warn("未找到service内容类型，请检查后台内容类型配置"),o.value=[],r.value=0}else console.warn("获取内容类型失败或内容类型数据为空:",e),o.value=[],r.value=0}catch(e){console.error("获取服务数据失败:",e),g.error("获取服务数据失败"),o.value=[],r.value=0}finally{u.value=!1}},f=(e,a)=>{n.value.pageNum=e,n.value.pageSize=a,p()},S=(e,a)=>{n.value.pageNum=1,n.value.pageSize=a,p()},k=e=>{console.log("查看服务详情:",e.title),e.contentId?y.push(`/service/detail/${e.contentId}`):g.info("服务详情功能开发中...")};return(e,a)=>{const d=h("a-spin"),s=h("a-pagination");return i(),l("div",D,[t("div",F,[a[2]||(a[2]=t("h1",{class:"page-title"},"服务中心",-1)),t("div",R,[a[1]||(a[1]=t("p",{class:"page-description"},"我会致力于为会员提供专业的服务与支持，促进软组织病研究与临床应用的发展。",-1)),t("div",V,[u.value?(i(),l("div",b,[_(d,{tip:"加载中..."})])):o.value.length===0?(i(),l("div",P," 暂无服务内容数据 ")):(i(),l("div",$,[(i(!0),l(x,null,q(o.value,(c,z)=>(i(),l("div",{class:"service-card",key:z},[t("div",j,[c.coverImage?(i(),l("img",{key:0,src:c.coverImage,alt:c.title},null,8,A)):(i(),l("div",E,"🛠️"))]),t("div",L,[t("h3",M,m(c.title),1),t("p",O,m(c.summary||c.description||"暂无描述"),1),t("a",{onClick:K=>k(c),class:"more-link"},"了解更多",8,U)])]))),128))])),o.value.length>0?(i(),l("div",G,[_(s,{current:n.value.pageNum,"onUpdate:current":a[0]||(a[0]=c=>n.value.pageNum=c),total:r.value,pageSize:n.value.pageSize,onChange:f,showSizeChanger:"",pageSizeOptions:["5","10","20"],onShowSizeChange:S},null,8,["current","total","pageSize"])])):T("",!0)])])])])}}},W=C(H,[["__scopeId","data-v-1e8f920b"]]);export{W as default};
