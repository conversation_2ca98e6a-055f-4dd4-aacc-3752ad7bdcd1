<template>
  <div class="main-layout" ref="layoutRef">
    <!-- 网站头部 -->
    <AppHeader />
    
    <!-- 主要内容区域 -->
    <main class="content-container">
      <slot></slot>
    </main>
    
    <!-- 网站底部 -->
    <AppFooter />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AppHeader from './AppHeader.vue'
import AppFooter from './AppFooter.vue'

const layoutRef = ref(null)
</script>

<style scoped>
.main-layout {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-container {
  flex: 1;
  width: 100%;
  position: relative;
  /* 不设置min-height: 101vh，避免导致额外滚动条 */
  min-height: 100%;
  overflow-y: auto; /* 使用auto而不是scroll */
}
</style>
