import Home from './views/Home.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/test-menu',
    name: 'TestMenu',
    component: () => import('./views/TestMenu.vue')
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('./views/About/index.vue')
  },
  {
    path: '/news',
    name: 'News',
    component: () => import('./views/News/index.vue')
  },
  {
    path: '/news/detail/:id',
    name: 'NewsDetail',
    component: () => import('./views/News/detail.vue')
  },
  {
    path: '/academic',
    name: 'Academic',
    component: () => import('./views/Academic/index.vue')
  },
  {
    path: '/party',
    name: 'Party',
    component: () => import('./views/Party/index.vue')
  },
  {
    path: '/party/detail/:id',
    name: 'PartyDetail',
    component: () => import('./views/Party/detail.vue')
  },
  {
    path: '/activity',
    name: 'Activity',
    component: () => import('./views/Activity/index.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/activity/detail/:id',
    name: 'ActivityDetail',
    component: () => import('./views/Activity/detail.vue')
  },
  {
    path: '/info',
    name: 'Info',
    component: () => import('./views/Info/index.vue')
  },
  {
    path: '/service',
    name: 'Service',
    component: () => import('./views/Service/index.vue')
  },
  {
    path: '/service/detail/:id',
    name: 'ServiceDetail',
    component: () => import('./views/Service/detail.vue')
  },
  {
    path: '/policy',
    name: 'Policy',
    component: () => import('./views/Policy/index.vue')
  },
  {
    path: '/policy/detail/:id',
    name: 'PolicyDetail',
    component: () => import('./views/Policy/detail.vue')
  },
  {
    path: '/join',
    name: 'Join',
    component: () => import('./views/Join/index.vue')
  },
  {
    path: '/branch',
    name: 'Branch',
    component: () => import('./views/Branch/index.vue')
  },
  {
    path: '/branch/detail/:id',
    name: 'BranchDetail',
    component: () => import('./views/Branch/detail.vue')
  },
  {
    path: '/branch-article/:id',
    name: 'BranchArticle',
    component: () => import('./views/Branch/article-detail.vue')
  },
  {
    path: '/cert',
    name: 'Certificate',
    component: () => import('./views/Certificate/index.vue')
  },
  {
    path: '/representative',
    name: 'Representative',
    component: () => import('./views/Representative/index.vue')
  },
  {
    path: '/representative/detail/:id',
    name: 'RepresentativeDetail',
    component: () => import('./views/Representative/detail.vue')
  },
  {
    path: '/regulation',
    name: 'Regulation',
    component: () => import('./views/Regulation/index.vue')
  },
  {
    path: '/regulation/detail/:id',
    name: 'RegulationDetail',
    component: () => import('./views/Regulation/detail.vue')
  },
  {
    path: '/regulation-detail',
    name: 'RegulationDetailQuery',
    component: () => import('./views/Regulation/detail.vue')
  },
  {
    path: '/registration',
    name: 'Registration',
    component: () => import('./views/Registration/index.vue')
  },
  {
    path: '/registration/payment',
    name: 'RegistrationPayment',
    component: () => import('./views/Registration/payment.vue')
  },
  {
    path: '/registration/complete',
    name: 'RegistrationComplete',
    component: () => import('./views/Registration/complete.vue')
  }
]

export default routes