import{_ as I,r as b,o as L,u as V,e as f,p as H,c,a,b as n,d as o,t as r,f as d,s as M,h as s,l as u,x as D,i,n as m,F as q,j as A}from"./index-DIypJWEE.js";import{A as O,F as R}from"./FileTextOutlined-BdIGULaK.js";import"./AntdIcon-C5sZj2-2.js";const S={class:"service-detail-container"},j={class:"container"},z={class:"detail-header"},E={class:"detail-title"},U={class:"detail-content"},$={key:0},G={class:"service-info"},J={class:"info-meta"},K={class:"service-tags"},P={key:0,class:"service-summary"},Q={class:"summary-content"},W=["innerHTML"],X={key:1,class:"service-attachments"},Y={class:"attachments-list"},Z=["href"],ee={key:1,class:"empty-data"},te={__name:"detail",setup(ae){const C=M(),y=V(),v=b(!1),t=b({});L(()=>{const l=y.query.id||y.params.id;l?N(l):f.error("缺少服务内容ID")});const N=async l=>{try{v.value=!0;const e=await H(l);e&&e.data?t.value=e.data:(t.value={},f.error("获取服务详情失败"))}catch(e){console.error("获取服务详情失败:",e),f.error("获取服务详情失败"),t.value={}}finally{v.value=!1}},w=l=>l?new Date(l).toLocaleDateString("zh-CN"):"",x=()=>{C.back()};return(l,e)=>{const F=d("a-button"),p=d("a-divider"),h=d("a-space"),_=d("a-tag"),T=d("a-empty"),B=d("a-spin");return s(),c("div",S,[a("div",j,[a("div",z,[n(F,{onClick:x,class:"back-btn"},{icon:o(()=>[n(D(O))]),default:o(()=>[e[0]||(e[0]=u(" 返回 "))]),_:1}),a("h1",E,r(t.value.title),1)]),a("div",U,[n(B,{spinning:v.value,tip:"加载中..."},{default:o(()=>[!v.value&&t.value.contentId?(s(),c("div",$,[a("div",G,[a("div",J,[n(h,null,{default:o(()=>[a("span",null,"分类："+r(t.value.contentTypeName||"服务"),1),n(p,{type:"vertical"}),a("span",null,"作者："+r(t.value.author||"管理员"),1),n(p,{type:"vertical"}),a("span",null,"来源："+r(t.value.source||"本会"),1),n(p,{type:"vertical"}),a("span",null,"发布时间："+r(w(t.value.publishTime)),1),n(p,{type:"vertical"}),a("span",null,"浏览量："+r(t.value.pageViewCount||0),1)]),_:1})]),a("div",K,[n(h,null,{default:o(()=>[t.value.topFlag?(s(),m(_,{key:0,color:"success"},{default:o(()=>e[1]||(e[1]=[u("置顶")])),_:1})):i("",!0),t.value.recommendFlag?(s(),m(_,{key:1,color:"success"},{default:o(()=>e[2]||(e[2]=[u("推荐")])),_:1})):i("",!0),t.value.status===0?(s(),m(_,{key:2,color:"warning"},{default:o(()=>e[3]||(e[3]=[u("草稿")])),_:1})):i("",!0),t.value.status===1?(s(),m(_,{key:3,color:"success"},{default:o(()=>e[4]||(e[4]=[u("已发布")])),_:1})):i("",!0),t.value.status===2?(s(),m(_,{key:4,color:"error"},{default:o(()=>e[5]||(e[5]=[u("已下线")])),_:1})):i("",!0)]),_:1})]),t.value.summary?(s(),c("div",P,[e[6]||(e[6]=a("div",{class:"summary-title"},"摘要：",-1)),a("div",Q,r(t.value.summary),1)])):i("",!0),a("div",{class:"service-content",innerHTML:t.value.contentHtml},null,8,W),t.value.attachment&&t.value.attachment.length>0?(s(),c("div",X,[e[7]||(e[7]=a("div",{class:"attachments-title"},"附件：",-1)),a("div",Y,[(s(!0),c(q,null,A(t.value.attachment,(k,g)=>(s(),c("div",{key:g,class:"attachment-item"},[a("a",{href:k.fileUrl,target:"_blank",class:"attachment-link"},[n(D(R)),u(" "+r(k.fileName||`附件${g+1}`),1)],8,Z)]))),128))])])):i("",!0)])])):!v.value&&!t.value.contentId?(s(),c("div",ee,[n(T,{description:"服务内容不存在或已被删除"})])):i("",!0)]),_:1},8,["spinning"])])])])}}},le=I(te,[["__scopeId","data-v-c8a84f31"]]);export{le as default};
