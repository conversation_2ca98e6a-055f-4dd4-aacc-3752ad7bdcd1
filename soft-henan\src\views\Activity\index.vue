<template>
  <div class="activity-container">
    <div class="container">
      <h1 class="page-title">活动信息</h1>
      <div class="activity-content">
        <a-spin :spinning="loading" tip="加载中...">
          <div class="min-height-container">
            <div class="activity-list" v-if="activityData.length > 0">
              <div class="activity-item" v-for="(item, index) in activityData" :key="item.contentId || index">
                <div class="activity-image">
                  <img v-if="item.image && item.image !== '/images/default-activity.jpg'" :src="item.image" :alt="item.title" />
                </div>
                <div class="activity-info">
                  <div class="activity-date">{{ formatDate(item.publishTime || item.createTime) }}</div>
                  <h3 class="activity-title">{{ item.title }}</h3>
                  <p class="activity-desc">{{ item.description || item.summary || '暂无描述' }}</p>
                  <router-link :to="`/activity/detail/${item.contentId}`" class="read-more">阅读更多</router-link>
                </div>
              </div>
            </div>
            <div class="empty-data" v-else>
              <a-empty description="暂无活动信息" />
            </div>
          </div>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { getAllContentTypes, queryContent } from '../../api/content'

// 响应式数据
const loading = ref(false)
const activityData = ref([])

// 页面加载
onMounted(() => {
  console.log('活动页面已加载')
  fetchActivityData()
})

// 获取活动数据
const fetchActivityData = async () => {
  try {
    loading.value = true
    
    // 先获取所有内容类型
    const typesRes = await getAllContentTypes()
    console.log('获取内容类型结果:', typesRes)
    
    if (typesRes && (typesRes.code === 0 || typesRes.code === 1 || typesRes.code === 200) && typesRes.data) {
      // 查找活动类型（使用精确的类型编码）
      const activityType = typesRes.data.find(type => 
        type.contentTypeCode === 'activity'
      )
      
      console.log('找到的活动类型:', activityType)
      
      if (activityType) {
        // 使用内容类型ID查询活动内容
        const params = {
          contentTypeId: activityType.contentTypeId,
          pageNum: 1,
          pageSize: 10,
          status: 1, // 已发布状态
          deletedFlag: false // 未删除
        }
        
        console.log('查询活动参数:', params)
        
        const contentRes = await queryContent(params)
        console.log('获取活动结果:', contentRes)
        
        if (contentRes && (contentRes.code === 0 || contentRes.code === 1 || contentRes.code === 200) && contentRes.data && contentRes.data.list) {
          // 转换数据格式以适配现有模板
          activityData.value = contentRes.data.list.map(item => ({
            title: item.title,
            publishTime: item.publishTime || item.createTime,
            description: item.description || item.summary || '暂无描述',
            image: item.coverImage || '/images/default-activity.jpg',
            contentId: item.contentId
          }))
          console.log('获取到的活动数据:', activityData.value)
        } else {
          console.warn('获取活动数据失败或无数据:', contentRes)
          activityData.value = []
        }
      } else {
        console.warn('未找到activity内容类型，请检查后台内容类型配置')
        activityData.value = []
      }
    } else {
      console.warn('获取内容类型失败或内容类型数据为空:', typesRes)
      activityData.value = []
    }
  } catch (error) {
    console.error('获取活动数据失败:', error)
    message.error('获取活动数据失败')
    activityData.value = []
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0 40px;
}

.page-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--primary-color);
}

.min-height-container {
  min-height: 400px;
}

.activity-content {
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.activity-item {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  padding: 20px;
}

.activity-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.activity-image {
  width: 200px;
  height: 150px;
  flex-shrink: 0;
  overflow: hidden;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  border-radius: 6px;
}

.activity-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.activity-date {
  color: #999;
  font-size: 12px;
  margin-bottom: 8px;
}

.activity-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

.activity-desc {
  color: #777;
  line-height: 1.5;
  margin-bottom: 8px;
  font-size: 13px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.read-more {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.read-more:hover {
  color: var(--primary-dark-color);
  text-decoration: underline;
}

.empty-data {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    width: 100%;
    padding: 15px;
  }
  
  .activity-item {
    flex-direction: column;
    padding: 15px;
  }
  
  .activity-image {
    width: 160px;
    height: 120px;
    margin-right: 0;
    margin-bottom: 15px;
  }
}
</style>