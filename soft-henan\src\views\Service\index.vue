<template>
  <div class="service-container">
    <div class="container">
      <h1 class="page-title">服务中心</h1>
      <div class="service-content">
        <p class="page-description">我会致力于为会员提供专业的服务与支持，促进软组织病研究与临床应用的发展。</p>

        <div class="min-height-container">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-container">
            <a-spin tip="加载中..."></a-spin>
          </div>

          <!-- 空数据状态 -->
          <div v-else-if="serviceItems.length === 0" class="empty-data">
            暂无服务内容数据
          </div>

          <!-- 服务卡片网格 -->
          <div v-else class="service-grid">
            <div class="service-card" v-for="(item, index) in serviceItems" :key="index">
              <div class="card-image">
                <img v-if="item.coverImage" :src="item.coverImage" :alt="item.title" />
                <div v-else class="default-icon">🛠️</div>
              </div>
              <div class="card-content">
                <h3 class="card-title">{{ item.title }}</h3>
                <p class="card-desc">{{ item.summary || item.description || '暂无描述' }}</p>
                <a @click="viewServiceDetail(item)" class="more-link">了解更多</a>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-container" v-if="serviceItems.length > 0">
            <a-pagination
              v-model:current="serviceParams.pageNum"
              :total="serviceTotal"
              :pageSize="serviceParams.pageSize"
              @change="handleServicePageChange"
              showSizeChanger
              :pageSizeOptions="['5', '10', '20']"
              @showSizeChange="handleServiceSizeChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { getAllContentTypes, queryContent } from '../../api/content'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const serviceItems = ref([])

// 分页参数
const serviceParams = ref({
  pageNum: 1,
  pageSize: 10
})

// 分页总数
const serviceTotal = ref(0)

// 页面加载
onMounted(() => {
  console.log('服务中心页面已加载')
  queryServiceData()
})

// 查询服务数据
const queryServiceData = async () => {
  try {
    loading.value = true
    
    // 先获取所有内容类型
    const typesRes = await getAllContentTypes()
    console.log('获取内容类型结果:', typesRes)
    
    if (typesRes && (typesRes.code === 0 || typesRes.code === 1 || typesRes.code === 200) && typesRes.data) {
      // 查找服务类型（使用精确的类型编码）
      const serviceType = typesRes.data.find(type => 
        type.contentTypeCode === 'service'
      )
      
      console.log('找到的服务类型:', serviceType)
      
      if (serviceType) {
        // 使用内容类型ID查询服务内容
        const params = {
          contentTypeId: serviceType.contentTypeId,
          pageNum: serviceParams.value.pageNum,
          pageSize: serviceParams.value.pageSize,
          status: 1, // 已发布状态
          deletedFlag: false // 未删除
        }
        
        console.log('查询服务参数:', params)
        
        const contentRes = await queryContent(params)
        console.log('获取服务结果:', contentRes)
        
        if (contentRes && (contentRes.code === 0 || contentRes.code === 1 || contentRes.code === 200) && contentRes.data && contentRes.data.list) {
          serviceItems.value = contentRes.data.list
          serviceTotal.value = contentRes.data.total || 0
          console.log('获取到的服务数据:', serviceItems.value)
        } else {
          console.warn('获取服务数据失败或无数据:', contentRes)
          serviceItems.value = []
          serviceTotal.value = 0
        }
      } else {
        console.warn('未找到service内容类型，请检查后台内容类型配置')
        serviceItems.value = []
        serviceTotal.value = 0
      }
    } else {
      console.warn('获取内容类型失败或内容类型数据为空:', typesRes)
      serviceItems.value = []
      serviceTotal.value = 0
    }
  } catch (error) {
    console.error('获取服务数据失败:', error)
    message.error('获取服务数据失败')
    serviceItems.value = []
    serviceTotal.value = 0
  } finally {
    loading.value = false
  }
}

// 服务分页处理
const handleServicePageChange = (page, pageSize) => {
  serviceParams.value.pageNum = page
  serviceParams.value.pageSize = pageSize
  queryServiceData()
}

const handleServiceSizeChange = (current, size) => {
  serviceParams.value.pageNum = 1
  serviceParams.value.pageSize = size
  queryServiceData()
}

// 查看服务详情
const viewServiceDetail = (item) => {
  console.log('查看服务详情:', item.title)
  if (item.contentId) {
    // 使用路由跳转到详情页面，而不是打开新窗口
    router.push(`/service/detail/${item.contentId}`)
  } else {
    message.info('服务详情功能开发中...')
  }
}
</script>

<style scoped>
.container {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0 40px;
}

.page-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--primary-color);
}

.page-description {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
  text-align: center;
}

.min-height-container {
  min-height: 400px;
}

.service-content {
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 20px;
}

.service-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
}

.service-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-3px);
}

.card-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-icon {
  font-size: 48px;
  color: #ccc;
  position: absolute;
  z-index: 1;
}

.card-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
}

.card-desc {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
  font-size: 14px;
  flex: 1;
}

.more-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
  cursor: pointer;
  align-self: flex-start;
}

.more-link:hover {
  color: var(--primary-dark-color);
  text-decoration: underline;
}

.loading-container {
  text-align: center;
  padding: 60px 20px;
}

.empty-data {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.pagination-container {
  text-align: center;
  margin-top: 30px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .service-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .container {
    width: 100%;
    padding: 15px;
  }
  
  .service-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
</style>