import{r as l,o as u,n as d,u as f,e as t,p,s as _,h as g}from"./index-DIypJWEE.js";import{C as m,n as h}from"./detailConfig-CjbJ0jXA.js";const C={__name:"detail",setup(D){const r=f(),s=_(),o=l(!1),n=l({}),c={...h,backHandler:()=>{const e=r.query.returnTo;e?s.push(e):s.push("/news")}},i=async()=>{try{o.value=!0;const e=r.params.id;if(!e){t.error("缺少新闻ID参数");return}const a=await p(e);a&&(a.code===0||a.code===1||a.code===200)&&a.data?n.value=a.data:(console.error("获取新闻详情失败，响应:",a),t.error("获取新闻详情失败"))}catch(e){console.error("获取新闻详情失败:",e),t.error("获取新闻详情失败")}finally{o.value=!1}};return u(()=>{i()}),(e,a)=>(g(),d(m,{detail:n.value,loading:o.value,config:c},null,8,["detail","loading"]))}};export{C as default};
